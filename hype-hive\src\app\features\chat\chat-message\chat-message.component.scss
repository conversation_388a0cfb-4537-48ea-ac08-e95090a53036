.message-container {
  display: flex;
  margin-bottom: 1rem;
  
  &.own-message {
    flex-direction: row-reverse;
    
    .message-content {
      align-items: flex-end;
    }
    
    .message-bubble {
      background-color: #e3f2fd;
      border-radius: 18px 18px 4px 18px;
      
      &::before {
        display: none;
      }
    }
  }
}

.avatar {
  margin-right: 0.5rem;
  
  img {
    width: 36px;
    height: 36px;
    border-radius: 50%;
    object-fit: cover;
  }
}

.message-content {
  display: flex;
  flex-direction: column;
  max-width: 70%;
}

.message-header {
  margin-bottom: 0.25rem;
  
  .sender-name {
    font-size: 0.875rem;
    font-weight: 500;
    color: #555;
  }
}

.message-bubble {
  position: relative;
  background-color: #fff;
  padding: 0.5rem 1rem;
  border-radius: 4px 18px 18px 18px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  
  .message-text {
    margin: 0;
    white-space: pre-wrap;
    word-break: break-word;
  }
  
  .message-time {
    display: block;
    font-size: 0.75rem;
    color: #888;
    margin-top: 0.25rem;
    text-align: right;
  }
}

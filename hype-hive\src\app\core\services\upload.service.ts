import { Injectable } from '@angular/core';
import { HttpClient, HttpEvent, HttpEventType, HttpRequest } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';

export interface UploadResponse {
  url: string;
  filename: string;
  mimetype: string;
  size: number;
}

@Injectable({
  providedIn: 'root'
})
export class UploadService {
  private apiUrl = `${environment.apiUrl}/upload`;
  
  constructor(private http: HttpClient) { }
  
  /**
   * Upload file
   * @param file File to upload
   */
  uploadFile(file: File): Observable<UploadResponse> {
    const formData = new FormData();
    formData.append('file', file);
    
    return this.http.post<any>(this.apiUrl, formData)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to upload file');
          }
        }),
        catchError(error => {
          console.error('Error uploading file:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to upload file'));
        })
      );
  }
  
  /**
   * Upload file with progress
   * @param file File to upload
   */
  uploadFileWithProgress(file: File): Observable<HttpEvent<any>> {
    const formData = new FormData();
    formData.append('file', file);
    
    const req = new HttpRequest('POST', this.apiUrl, formData, {
      reportProgress: true
    });
    
    return this.http.request(req);
  }
  
  /**
   * Delete file
   * @param fileUrl File URL to delete
   */
  deleteFile(fileUrl: string): Observable<boolean> {
    return this.http.delete<any>(this.apiUrl, { body: { fileUrl } })
      .pipe(
        map(response => {
          if (response.success) {
            return true;
          } else {
            throw new Error(response.message || 'Failed to delete file');
          }
        }),
        catchError(error => {
          console.error('Error deleting file:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to delete file'));
        })
      );
  }
  
  /**
   * Get file type from file
   * @param file File
   */
  getFileType(file: File): 'image' | 'video' | 'unknown' {
    if (file.type.startsWith('image/')) {
      return 'image';
    } else if (file.type.startsWith('video/')) {
      return 'video';
    } else {
      return 'unknown';
    }
  }
  
  /**
   * Check if file is valid (image or video)
   * @param file File to check
   */
  isValidFile(file: File): boolean {
    return this.getFileType(file) !== 'unknown';
  }
  
  /**
   * Check if file size is valid (less than 10MB)
   * @param file File to check
   */
  isValidFileSize(file: File): boolean {
    const maxSize = 10 * 1024 * 1024; // 10MB
    return file.size <= maxSize;
  }
}

const TestDatabase = require('./helpers/testDb');

let testDb;

// Setup test database before all tests
beforeAll(async () => {
  testDb = new TestDatabase();
  await testDb.connect();
});

// Clean up after each test
afterEach(async () => {
  if (testDb) {
    await testDb.clearDatabase();
  }
});

// Close database connection after all tests
afterAll(async () => {
  if (testDb) {
    await testDb.disconnect();
  }
});

// Global test utilities
global.testUtils = {
  createTestUser: () => ({
    discordId: '123456789012345678',
    discordUsername: 'testuser#1234',
    discordAvatar: 'avatar_url',
    email: '<EMAIL>',
    bio: 'Test user bio',
    proficiencies: [
      {
        name: 'OBS Setup',
        category: 'Streaming Platforms',
        isSelected: true
      },
      {
        name: 'Discord Bots',
        category: 'Bots',
        isSelected: false
      }
    ],
    socialLinks: {
      twitch: 'https://twitch.tv/testuser',
      twitter: 'https://twitter.com/testuser'
    }
  }),

  createTestMentor: () => ({
    discordId: '987654321012345678',
    discordUsername: 'testmentor#5678',
    discordAvatar: 'mentor_avatar_url',
    email: '<EMAIL>',
    bio: 'Experienced streamer and content creator',
    role: 'mentor',
    isMentor: true,
    proficiencies: [
      {
        name: 'Advanced Streaming',
        category: 'Streaming Platforms',
        isSelected: true
      },
      {
        name: 'Community Management',
        category: 'Networking',
        isSelected: true
      }
    ],
    socialLinks: {
      twitch: 'https://twitch.tv/testmentor',
      youtube: 'https://youtube.com/testmentor'
    }
  }),

  // Seed test data for integration tests
  seedTestData: async () => {
    if (testDb) {
      return await testDb.seedTestData();
    }
    throw new Error('Test database not initialized');
  },

  // Get test database instance
  getTestDb: () => testDb
};

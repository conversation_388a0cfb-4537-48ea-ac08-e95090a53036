import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable } from 'rxjs';
import { User } from '../models/user.model';
import { environment } from '../../../environments/environment';

@Injectable({
  providedIn: 'root'
})
export class UserService {

  constructor(private http: HttpClient) { }

  /**
   * Search users by username
   * @param term Search term
   */
  searchUsers(term: string): Observable<User[]> {
    return this.http.get<User[]>(`${environment.apiUrl}/users/search`, { params: { q: term } });
  }
}

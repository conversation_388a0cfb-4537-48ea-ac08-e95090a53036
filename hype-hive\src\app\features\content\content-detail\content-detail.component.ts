import { Component, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBar, MatSnackBarModule } from '@angular/material/snack-bar';
import { MatDividerModule } from '@angular/material/divider';
import { DomSanitizer, SafeResourceUrl } from '@angular/platform-browser';
import { Subject, takeUntil } from 'rxjs';

import { ContentService, Content } from '../../../core/services/content.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-content-detail',
  standalone: true,
  imports: [
    CommonModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDividerModule
  ],
  templateUrl: './content-detail.component.html',
  styleUrl: './content-detail.component.scss'
})
export class ContentDetailComponent implements OnInit, OnDestroy {
  content: Content | null = null;
  isLoading = true;
  isLiked = false;
  currentUser: any = null;
  safeEmbedUrl: SafeResourceUrl | null = null;

  private destroy$ = new Subject<void>();

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private contentService: ContentService,
    private authService: AuthService,
    private sanitizer: DomSanitizer,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    // Get current user
    this.currentUser = this.authService.getCurrentUser();

    // Subscribe to auth changes
    this.authService.currentUser$.pipe(takeUntil(this.destroy$)).subscribe(user => {
      this.currentUser = user;
    });

    // Get content ID from route and load content
    this.route.params.pipe(takeUntil(this.destroy$)).subscribe(params => {
      const contentId = params['id'];
      if (contentId) {
        this.loadContent(contentId);
      }
    });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Load content by ID
   */
  loadContent(contentId: string): void {
    this.isLoading = true;

    this.contentService.getContentById(contentId)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (content) => {
          this.content = content;
          this.isLiked = this.checkIfLiked();
          this.setupEmbedUrl();
          this.isLoading = false;
        },
        error: (error) => {
          console.error('Error loading content:', error);
          this.snackBar.open('Failed to load content', 'Close', { duration: 3000 });
          this.isLoading = false;
          this.router.navigate(['/content']);
        }
      });
  }

  /**
   * Check if current user has liked this content
   */
  checkIfLiked(): boolean {
    if (!this.content || !this.currentUser) return false;
    return this.content.likedBy?.includes(this.currentUser._id) || false;
  }

  /**
   * Setup safe embed URL for video
   */
  setupEmbedUrl(): void {
    if (this.content?.embedUrl) {
      this.safeEmbedUrl = this.sanitizer.bypassSecurityTrustResourceUrl(this.content.embedUrl);
    }
  }

  /**
   * Toggle like on content
   */
  toggleLike(): void {
    if (!this.content || !this.currentUser) {
      this.snackBar.open('Please log in to like content', 'Close', { duration: 3000 });
      return;
    }

    this.contentService.toggleLike(this.content._id)
      .pipe(takeUntil(this.destroy$))
      .subscribe({
        next: (response) => {
          // Update the content with new like data
          if (this.content) {
            this.content.likes = response.likes;
            this.isLiked = response.liked;
          }
          const message = this.isLiked ? 'Content liked!' : 'Like removed';
          this.snackBar.open(message, 'Close', { duration: 2000 });
        },
        error: (error) => {
          console.error('Error toggling like:', error);
          this.snackBar.open('Failed to update like', 'Close', { duration: 3000 });
        }
      });
  }

  /**
   * Get difficulty badge class
   */
  getDifficultyClass(difficulty: string): string {
    return `difficulty-${difficulty}`;
  }

  /**
   * Navigate back to content list
   */
  goBack(): void {
    this.router.navigate(['/content']);
  }
}

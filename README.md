# HypeHive - Twitch Streaming Education Platform

HypeHive is a comprehensive education platform for Twitch streamers, connecting learners with experienced mentors across various streaming-related topics.

## Features

- Discord authentication for seamless login
- User profiles showing proficiencies across 8 instruction areas
- Mentor search and filtering
- Educational content management
- Video uploads and embedding
- Content recommendation system
- Responsive design for all devices

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- MongoDB
- Discord Developer Application

### Setting Up Discord OAuth

1. Go to the [Discord Developer Portal](https://discord.com/developers/applications)
2. Click "New Application" and give it a name (e.g., "HypeHive")
3. Navigate to the "OAuth2" section in the sidebar
4. Add the following redirect URLs (server-side callback):
   - For development: `http://localhost:3000/api/auth/discord/callback`
   - For production: `https://your-production-url.com/api/auth/discord/callback`
5. Save changes
6. Copy the "Client ID" and "Client Secret" for the next steps

**Note**: The OAuth flow now uses server-side callbacks for enhanced security.

### Environment Configuration

#### Backend (.env file)

```
# Server Configuration
PORT=3000
NODE_ENV=development

# MongoDB Configuration
MONGODB_URI=mongodb://localhost:27017/hype-hive

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d

# Discord OAuth Configuration
DISCORD_CLIENT_ID=your_discord_client_id
DISCORD_CLIENT_SECRET=your_discord_client_secret
DISCORD_REDIRECT_URI=http://localhost:3000/api/auth/discord/callback
DISCORD_API_ENDPOINT=https://discord.com/api/v10

# Frontend URL
FRONTEND_URL=http://localhost:4200

# File Upload Configuration
UPLOAD_DIR=uploads
MAX_FILE_SIZE=10485760 # 10MB

# Logging Configuration
LOG_LEVEL=debug
```

#### Frontend (environment.ts)

Update the Discord client ID in `hype-hive/src/environments/environment.ts`:

```typescript
export const environment = {
  production: false,
  discord: {
    clientId: 'YOUR_DISCORD_CLIENT_ID',
    redirectUri: 'http://localhost:3000/api/auth/discord/callback',
    apiEndpoint: 'https://discord.com/api/v10'
  },
  apiUrl: 'http://localhost:3000/api',
  monitoring: {
    errorTrackingEnabled: true,
    analyticsEnabled: true
  }
};
```

### Installation

1. Clone the repository
   ```
   git clone https://github.com/yourusername/hype-hive.git
   cd hype-hive
   ```

2. Install backend dependencies
   ```
   cd backend
   npm install
   ```

3. Install frontend dependencies
   ```
   cd ../hype-hive
   npm install
   ```

### Running the Application

1. Start the backend server
   ```
   cd backend
   npm run dev
   ```

2. Start the frontend application
   ```
   cd ../hype-hive
   npm start
   ```

3. Open your browser and navigate to `http://localhost:4200`

## Deployment

### Backend Deployment

1. Set up environment variables on your server
2. Run the deployment script
   ```
   cd backend
   node deploy.js
   ```
3. Start the server
   ```
   npm start
   ```

### Frontend Deployment

1. Set up environment variables on your server
2. Run the deployment script
   ```
   cd hype-hive
   node deploy.js
   ```
3. Deploy the contents of the `dist/` directory to your web server

## Monitoring and Error Tracking

The application includes built-in monitoring and error tracking. To view logs:

- Backend logs are stored in the `backend/logs/` directory
- Frontend errors are sent to the backend API and stored in the database

## License

This project is licensed under the MIT License - see the LICENSE file for details.

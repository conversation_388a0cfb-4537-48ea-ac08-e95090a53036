<div class="message-container" [ngClass]="{'own-message': isOwnMessage}">
  <div class="avatar" *ngIf="!isOwnMessage">
    <img [src]="getSenderAvatar()" alt="Avatar">
  </div>
  
  <div class="message-content">
    <div class="message-header" *ngIf="!isOwnMessage">
      <span class="sender-name">{{ getSenderName() }}</span>
    </div>
    
    <div class="message-bubble">
      <p class="message-text">{{ message.content }}</p>
      <span class="message-time" [matTooltip]="isSentToday() ? 'Today ' + formatTime() : formatDate() + ' ' + formatTime()">
        {{ formatTime() }}
      </span>
    </div>
  </div>
</div>

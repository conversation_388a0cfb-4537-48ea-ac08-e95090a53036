/**
 * Deployment script for HypeHive frontend
 * 
 * This script prepares the application for deployment to production.
 * It can be run with: node deploy.js
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configuration
const config = {
  // Environment variables to check
  requiredEnvVars: [
    'DISCORD_CLIENT_ID',
    'DISCORD_REDIRECT_URI',
    'API_URL'
  ],
  
  // Environment file path
  envFilePath: path.join(__dirname, 'src/environments/environment.prod.ts')
};

/**
 * Check required environment variables
 */
function checkEnvironmentVariables() {
  console.log('Checking environment variables...');
  
  const missingVars = [];
  
  config.requiredEnvVars.forEach(envVar => {
    if (!process.env[envVar]) {
      missingVars.push(envVar);
    }
  });
  
  if (missingVars.length > 0) {
    console.error('Error: Missing required environment variables:');
    missingVars.forEach(envVar => {
      console.error(`- ${envVar}`);
    });
    console.error('Please set these environment variables before deploying.');
    process.exit(1);
  }
  
  console.log('All required environment variables are set.');
}

/**
 * Update environment.prod.ts file with environment variables
 */
function updateEnvironmentFile() {
  console.log('Updating environment.prod.ts file...');
  
  try {
    const envFileContent = `export const environment = {
  production: true,
  discord: {
    clientId: '${process.env.DISCORD_CLIENT_ID}',
    redirectUri: '${process.env.DISCORD_REDIRECT_URI}',
    apiEndpoint: 'https://discord.com/api/v10'
  },
  apiUrl: '${process.env.API_URL}',
  monitoring: {
    errorTrackingEnabled: true,
    analyticsEnabled: true
  }
};`;
    
    fs.writeFileSync(config.envFilePath, envFileContent);
    console.log('Environment file updated successfully.');
  } catch (error) {
    console.error('Error updating environment file:', error.message);
    process.exit(1);
  }
}

/**
 * Build the application for production
 */
function buildApplication() {
  console.log('Building application for production...');
  
  try {
    execSync('npm run build', { stdio: 'inherit' });
    console.log('Application built successfully.');
  } catch (error) {
    console.error('Error building application:', error.message);
    process.exit(1);
  }
}

/**
 * Main deployment function
 */
function deploy() {
  console.log('Starting deployment process...');
  
  // Check environment variables
  checkEnvironmentVariables();
  
  // Update environment file
  updateEnvironmentFile();
  
  // Build application
  buildApplication();
  
  console.log('Deployment completed successfully!');
  console.log('The production build is available in the dist/ directory.');
}

// Run deployment
deploy();

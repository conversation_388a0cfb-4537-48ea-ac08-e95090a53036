const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');

class TestDatabase {
  constructor() {
    this.mongoServer = null;
    this.connection = null;
  }

  async connect() {
    try {
      // Create in-memory MongoDB instance
      this.mongoServer = await MongoMemoryServer.create({
        instance: {
          dbName: 'hype-hive-test'
        }
      });

      const mongoUri = this.mongoServer.getUri();
      
      // Connect to the in-memory database
      this.connection = await mongoose.connect(mongoUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });

      console.log('Test database connected successfully');
      return this.connection;
    } catch (error) {
      console.error('Test database connection failed:', error);
      throw error;
    }
  }

  async disconnect() {
    try {
      if (this.connection) {
        await mongoose.connection.dropDatabase();
        await mongoose.connection.close();
      }
      
      if (this.mongoServer) {
        await this.mongoServer.stop();
      }
      
      console.log('Test database disconnected successfully');
    } catch (error) {
      console.error('Test database disconnection failed:', error);
      throw error;
    }
  }

  async clearDatabase() {
    try {
      const collections = mongoose.connection.collections;
      
      for (const key in collections) {
        const collection = collections[key];
        await collection.deleteMany({});
      }
      
      console.log('Test database cleared successfully');
    } catch (error) {
      console.error('Test database clear failed:', error);
      throw error;
    }
  }

  async seedTestData() {
    try {
      const User = require('../../src/models/User');
      
      // Create test users
      const testUsers = [
        {
          discordId: 'test_user_1_123456789',
          discordUsername: 'testuser1#1234',
          discordAvatar: 'avatar1.jpg',
          email: '<EMAIL>',
          bio: 'Test user 1 bio',
          proficiencies: [
            {
              name: 'Basic Streaming',
              category: 'Streaming Platforms',
              isSelected: true
            },
            {
              name: 'Content Planning',
              category: 'Account Setup',
              isSelected: true
            }
          ],
          socialLinks: {
            twitch: 'https://twitch.tv/testuser1'
          }
        },
        {
          discordId: 'test_mentor_1_987654321',
          discordUsername: 'testmentor1#5678',
          discordAvatar: 'mentor1.jpg',
          email: '<EMAIL>',
          bio: 'Experienced streamer with 5 years of experience',
          role: 'mentor',
          isMentor: true,
          proficiencies: [
            {
              name: 'Advanced Streaming',
              category: 'Streaming Platforms',
              isSelected: true
            },
            {
              name: 'Community Building',
              category: 'Networking',
              isSelected: true
            },
            {
              name: 'Bot Management',
              category: 'Bots',
              isSelected: true
            }
          ],
          socialLinks: {
            twitch: 'https://twitch.tv/testmentor1',
            youtube: 'https://youtube.com/testmentor1'
          }
        },
        {
          discordId: 'test_user_2_456789123',
          discordUsername: 'testuser2#9012',
          discordAvatar: 'avatar2.jpg',
          email: '<EMAIL>',
          bio: 'Aspiring content creator',
          mentorApplication: {
            status: 'pending',
            submittedAt: new Date()
          },
          proficiencies: [
            {
              name: 'Intermediate Streaming',
              category: 'Streaming Platforms',
              isSelected: true
            },
            {
              name: 'Basic Emotes',
              category: 'Emotes',
              isSelected: false
            }
          ],
          socialLinks: {
            twitch: 'https://twitch.tv/testuser2'
          }
        }
      ];

      const createdUsers = await User.insertMany(testUsers);
      console.log(`Seeded ${createdUsers.length} test users`);
      
      return {
        users: createdUsers
      };
    } catch (error) {
      console.error('Test data seeding failed:', error);
      throw error;
    }
  }

  getConnectionString() {
    return this.mongoServer ? this.mongoServer.getUri() : null;
  }

  isConnected() {
    return mongoose.connection.readyState === 1;
  }
}

module.exports = TestDatabase;

import { RenderMode, ServerRoute } from '@angular/ssr';

export function getPrerenderParams(route: string) {
  // For demo, return empty array for dynamic routes
  // In production, fetch IDs from DB or API
  if (route === 'mentors/:id') {
    return [];
  }
  if (route === 'content/:id') {
    return [];
  }
  if (route === 'chat/:id') {
    return [];
  }
  return undefined;
}

export const serverRoutes: ServerRoute[] = [
  {
    path: 'mentors/:id',
    renderMode: RenderMode.Server
  },
  {
    path: 'content/:id',
    renderMode: RenderMode.Server
  },
  {
    path: 'chat/:id',
    renderMode: RenderMode.Server
  },
  {
    path: '**',
    renderMode: RenderMode.Prerender
  }
];

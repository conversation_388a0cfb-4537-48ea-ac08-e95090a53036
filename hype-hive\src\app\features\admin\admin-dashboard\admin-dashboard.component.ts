import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatTableModule } from '@angular/material/table';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatTooltipModule } from '@angular/material/tooltip';

import { AdminService, SystemStats } from '../../../core/services/admin.service';
import { User } from '../../../core/models/user.model';

@Component({
  selector: 'app-admin-dashboard',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatTableModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatTooltipModule
  ],
  templateUrl: './admin-dashboard.component.html',
  styleUrl: './admin-dashboard.component.scss'
})
export class AdminDashboardComponent implements OnInit {
  stats: SystemStats | null = null;
  pendingApplications: User[] = [];
  recentUsers: User[] = [];
  loading = true;
  error: string | null = null;

  displayedColumns: string[] = ['username', 'email', 'role', 'status', 'actions'];

  constructor(
    private adminService: AdminService,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.loadDashboardData();
  }

  private loadDashboardData(): void {
    this.loading = true;
    this.error = null;

    // Load system stats
    this.adminService.getSystemStats().subscribe({
      next: (stats) => {
        this.stats = stats;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading system stats:', error);
        this.error = 'Failed to load dashboard data';
        this.loading = false;
      }
    });

    // Load pending mentor applications
    this.adminService.getPendingMentorApplications().subscribe({
      next: (applications) => {
        this.pendingApplications = applications;
      },
      error: (error) => {
        console.error('Error loading pending applications:', error);
      }
    });

    // Load recent users
    this.adminService.getAllUsers({ limit: 5, sort: 'createdAt' }).subscribe({
      next: (response) => {
        this.recentUsers = response.data;
      },
      error: (error) => {
        console.error('Error loading recent users:', error);
      }
    });
  }

  approveMentorApplication(userId: string): void {
    this.adminService.reviewMentorApplication(userId, 'approved').subscribe({
      next: () => {
        this.snackBar.open('Mentor application approved', 'Close', { duration: 3000 });
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error approving application:', error);
        this.snackBar.open('Failed to approve application', 'Close', { duration: 3000 });
      }
    });
  }

  rejectMentorApplication(userId: string): void {
    this.adminService.reviewMentorApplication(userId, 'rejected').subscribe({
      next: () => {
        this.snackBar.open('Mentor application rejected', 'Close', { duration: 3000 });
        this.loadDashboardData(); // Refresh data
      },
      error: (error) => {
        console.error('Error rejecting application:', error);
        this.snackBar.open('Failed to reject application', 'Close', { duration: 3000 });
      }
    });
  }

  toggleUserStatus(user: User): void {
    const newStatus = !user.isActive;
    this.adminService.toggleUserStatus(user.id, newStatus).subscribe({
      next: () => {
        user.isActive = newStatus;
        const action = newStatus ? 'activated' : 'deactivated';
        this.snackBar.open(`User ${action}`, 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('Error updating user status:', error);
        this.snackBar.open('Failed to update user status', 'Close', { duration: 3000 });
      }
    });
  }

  refresh(): void {
    this.loadDashboardData();
  }
}

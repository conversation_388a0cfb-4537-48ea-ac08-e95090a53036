<div class="content-management">
  <div class="page-header">
    <h1>Content Management</h1>
    <button mat-raised-button color="primary" (click)="loadContent()" [disabled]="loading">
      <mat-icon>refresh</mat-icon>
      Refresh
    </button>
  </div>

  <!-- Filters -->
  <mat-card class="filters-card">
    <mat-card-content>
      <div class="filters">
        <mat-form-field appearance="outline">
          <mat-label>Search content</mat-label>
          <input matInput [(ngModel)]="searchTerm" (keyup.enter)="onSearch()" placeholder="Title or description">
          <mat-icon matSuffix>search</mat-icon>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Category</mat-label>
          <mat-select [(ngModel)]="categoryFilter" (selectionChange)="onFilterChange()">
            <mat-option value="">All Categories</mat-option>
            <mat-option value="Account Setup">Account Setup</mat-option>
            <mat-option value="Bots">Bots</mat-option>
            <mat-option value="Networking">Networking</mat-option>
            <mat-option value="Emotes">Emotes</mat-option>
            <mat-option value="Streaming Platforms">Streaming Platforms</mat-option>
            <mat-option value="Affiliate Roles">Affiliate Roles</mat-option>
            <mat-option value="Console Help">Console Help</mat-option>
            <mat-option value="PC Help">PC Help</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Status</mat-label>
          <mat-select [(ngModel)]="statusFilter" (selectionChange)="onFilterChange()">
            <mat-option value="">All Status</mat-option>
            <mat-option value="true">Published</mat-option>
            <mat-option value="false">Draft</mat-option>
          </mat-select>
        </mat-form-field>

        <mat-form-field appearance="outline">
          <mat-label>Difficulty</mat-label>
          <mat-select [(ngModel)]="difficultyFilter" (selectionChange)="onFilterChange()">
            <mat-option value="">All Levels</mat-option>
            <mat-option value="beginner">Beginner</mat-option>
            <mat-option value="intermediate">Intermediate</mat-option>
            <mat-option value="advanced">Advanced</mat-option>
          </mat-select>
        </mat-form-field>

        <div class="filter-actions">
          <button mat-button color="primary" (click)="onSearch()">
            <mat-icon>search</mat-icon>
            Search
          </button>
          <button mat-button (click)="clearFilters()">
            <mat-icon>clear</mat-icon>
            Clear
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading content...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-button color="primary" (click)="loadContent()">Try Again</button>
  </div>

  <!-- Content Table -->
  <mat-card *ngIf="!loading && !error" class="content-table-card">
    <mat-card-header>
      <mat-card-title>Content ({{ totalContent }})</mat-card-title>
    </mat-card-header>
    <mat-card-content>
      <table mat-table [dataSource]="content" class="content-table">
        <!-- Title Column -->
        <ng-container matColumnDef="title">
          <th mat-header-cell *matHeaderCellDef>Title</th>
          <td mat-cell *matCellDef="let item">
            <div class="content-cell">
              <img *ngIf="item.thumbnailUrl" [src]="item.thumbnailUrl" [alt]="item.title" class="thumbnail">
              <div class="content-info">
                <div class="title">{{ item.title }}</div>
                <div class="description">{{ item.description | slice:0:100 }}{{ item.description?.length > 100 ? '...' : '' }}</div>
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Creator Column -->
        <ng-container matColumnDef="creator">
          <th mat-header-cell *matHeaderCellDef>Creator</th>
          <td mat-cell *matCellDef="let item">
            <div class="creator-cell">
              <img [src]="item.createdBy?.discordAvatar" [alt]="item.createdBy?.discordUsername" class="avatar">
              {{ item.createdBy?.discordUsername || 'Unknown' }}
            </div>
          </td>
        </ng-container>

        <!-- Category Column -->
        <ng-container matColumnDef="category">
          <th mat-header-cell *matHeaderCellDef>Category</th>
          <td mat-cell *matCellDef="let item">
            <mat-chip color="primary">{{ item.category }}</mat-chip>
          </td>
        </ng-container>

        <!-- Difficulty Column -->
        <ng-container matColumnDef="difficulty">
          <th mat-header-cell *matHeaderCellDef>Difficulty</th>
          <td mat-cell *matCellDef="let item">
            <mat-chip [color]="getDifficultyColor(item.difficulty)">
              {{ item.difficulty }}
            </mat-chip>
          </td>
        </ng-container>

        <!-- Status Column -->
        <ng-container matColumnDef="status">
          <th mat-header-cell *matHeaderCellDef>Status</th>
          <td mat-cell *matCellDef="let item">
            <mat-slide-toggle
              [checked]="item.isPublished"
              (change)="toggleContentStatus(item)"
              [color]="'primary'">
              {{ item.isPublished ? 'Published' : 'Draft' }}
            </mat-slide-toggle>
          </td>
        </ng-container>

        <!-- Stats Column -->
        <ng-container matColumnDef="stats">
          <th mat-header-cell *matHeaderCellDef>Stats</th>
          <td mat-cell *matCellDef="let item">
            <div class="stats-cell">
              <div class="stat">
                <mat-icon>visibility</mat-icon>
                {{ item.views || 0 }}
              </div>
              <div class="stat">
                <mat-icon>favorite</mat-icon>
                {{ item.likes || 0 }}
              </div>
            </div>
          </td>
        </ng-container>

        <!-- Actions Column -->
        <ng-container matColumnDef="actions">
          <th mat-header-cell *matHeaderCellDef>Actions</th>
          <td mat-cell *matCellDef="let item">
            <div class="action-buttons">
              <button mat-icon-button
                      color="primary"
                      [routerLink]="['/content', item._id]"
                      matTooltip="View Content">
                <mat-icon>visibility</mat-icon>
              </button>

              <button mat-icon-button
                      color="accent"
                      matTooltip="Edit Content">
                <mat-icon>edit</mat-icon>
              </button>

              <button mat-icon-button
                      color="warn"
                      (click)="deleteContent(item)"
                      matTooltip="Delete Content">
                <mat-icon>delete</mat-icon>
              </button>
            </div>
          </td>
        </ng-container>

        <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
        <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
      </table>

      <!-- Pagination -->
      <mat-paginator
        [length]="totalContent"
        [pageSize]="pageSize"
        [pageSizeOptions]="pageSizeOptions"
        [pageIndex]="currentPage"
        (page)="onPageChange($event)"
        showFirstLastButtons>
      </mat-paginator>
    </mat-card-content>
  </mat-card>
</div>

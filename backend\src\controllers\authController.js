const authService = require('../services/authService');
const logger = require('../config/logger');
const jwt = require('jsonwebtoken');

/**
 * Handle Discord OAuth callback
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const handleDiscordCallback = async (req, res) => {
  try {
    const { code } = req.body;

    if (!code) {
      return res.status(400).json({ 
        success: false, 
        message: 'Authorization code is required' 
      });
    }

    // Exchange code for token
    const tokenData = await authService.exchangeCodeForToken(code);
    
    // Get Discord user profile
    const discordProfile = await authService.getDiscordUserProfile(tokenData.access_token);
    
    // Find or create user
    const user = await authService.findOrCreateUser(discordProfile);
    
    // Generate JWT token
    const token = authService.generateToken(user);

    // Return user data and token
    return res.status(200).json({
      success: true,
      token,
      user: {
        id: user._id,
        discordId: user.discordId,
        discordUsername: user.discordUsername,
        discordAvatar: user.discordAvatar,
        email: user.email,
        isMentor: user.isMentor,
        proficiencies: user.proficiencies,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Discord callback error: ${error.message}`);
    return res.status(500).json({ 
      success: false, 
      message: 'Authentication failed',
      error: error.message
    });
  }
};

/**
 * Get current user profile
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getCurrentUser = async (req, res) => {
  try {
    // User is already attached to req by auth middleware
    const user = req.user;
    
    return res.status(200).json({
      success: true,
      user: {
        id: user._id,
        discordId: user.discordId,
        discordUsername: user.discordUsername,
        discordAvatar: user.discordAvatar,
        email: user.email,
        isMentor: user.isMentor,
        proficiencies: user.proficiencies,
        role: user.role,
        bio: user.bio,
        socialLinks: user.socialLinks,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Get current user error: ${error.message}`);
    return res.status(500).json({ 
      success: false, 
      message: 'Failed to get user profile',
      error: error.message
    });
  }
};

/**
 * Handle Discord OAuth callback (server-side redirect)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const handleDiscordCallbackServer = async (req, res) => {
  try {
    const { code, error, state } = req.query;

    // Handle OAuth errors
    if (error) {
      logger.warn(`Discord OAuth error: ${error}`);
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4200';
      return res.redirect(`${frontendUrl}/auth/login?error=${encodeURIComponent(error)}`);
    }

    if (!code) {
      logger.warn('No authorization code received from Discord');
      const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4200';
      return res.redirect(`${frontendUrl}/auth/login?error=${encodeURIComponent('No authorization code received')}`);
    }

    // Exchange code for token
    const tokenData = await authService.exchangeCodeForToken(code);

    // Get Discord user profile
    const discordProfile = await authService.getDiscordUserProfile(tokenData.access_token);

    // Find or create user
    const user = await authService.findOrCreateUser(discordProfile);

    // Generate JWT token
    const token = authService.generateToken(user);

    // Set secure HTTP-only cookie with JWT
    const isProduction = process.env.NODE_ENV === 'production';
    res.cookie('auth_token', token, {
      httpOnly: true,
      secure: isProduction, // Only send over HTTPS in production
      sameSite: isProduction ? 'strict' : 'lax',
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7 days
      path: '/'
    });

    // Redirect to frontend with success
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4200';
    const redirectUrl = state ? decodeURIComponent(state) : '/profile';

    logger.info(`User authenticated successfully: ${user.discordUsername} (${user.discordId})`);
    return res.redirect(`${frontendUrl}${redirectUrl}?auth=success`);

  } catch (error) {
    logger.error(`Discord server callback error: ${error.message}`);
    const frontendUrl = process.env.FRONTEND_URL || 'http://localhost:4200';
    return res.redirect(`${frontendUrl}/auth/login?error=${encodeURIComponent('Authentication failed')}`);
  }
};

/**
 * Handle user logout
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const logout = async (req, res) => {
  try {
    // Clear the auth cookie
    res.clearCookie('auth_token', {
      path: '/',
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: process.env.NODE_ENV === 'production' ? 'strict' : 'lax'
    });

    logger.info(`User logged out: ${req.user ? req.user.discordUsername : 'Unknown'}`);

    return res.status(200).json({
      success: true,
      message: 'Logged out successfully'
    });
  } catch (error) {
    logger.error(`Logout error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Logout failed',
      error: error.message
    });
  }
};

module.exports = {
  handleDiscordCallback,
  handleDiscordCallbackServer,
  getCurrentUser,
  logout
};

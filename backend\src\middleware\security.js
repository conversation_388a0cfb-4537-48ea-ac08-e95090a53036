const rateLimit = require('express-rate-limit');
const helmet = require('helmet');
const mongoSanitize = require('express-mongo-sanitize');
const logger = require('../config/logger');

/**
 * General rate limiting middleware
 * More lenient in development, stricter in production
 */
const generalLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 100 : 1000, // 100 in prod, 1000 in dev
  message: {
    success: false,
    message: 'Too many requests from this IP, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many requests from this IP, please try again later.'
    });
  }
});

/**
 * Strict rate limiting for authentication endpoints
 * More lenient in development for testing
 */
const authLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 5 : 50, // 5 in prod, 50 in dev
  message: {
    success: false,
    message: 'Too many authentication attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Auth rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many authentication attempts, please try again later.'
    });
  }
});

/**
 * Rate limiting for content creation
 */
const contentCreationLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 10, // Limit each IP to 10 content creations per hour
  message: {
    success: false,
    message: 'Too many content creation attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Content creation rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many content creation attempts, please try again later.'
    });
  }
});

/**
 * Rate limiting for file uploads
 */
const uploadLimiter = rateLimit({
  windowMs: 60 * 60 * 1000, // 1 hour
  max: 20, // Limit each IP to 20 uploads per hour
  message: {
    success: false,
    message: 'Too many upload attempts, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Upload rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many upload attempts, please try again later.'
    });
  }
});

/**
 * Rate limiting for chat messages
 */
const chatLimiter = rateLimit({
  windowMs: 60 * 1000, // 1 minute
  max: 30, // Limit each IP to 30 messages per minute
  message: {
    success: false,
    message: 'Too many messages, please slow down.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Chat rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many messages, please slow down.'
    });
  }
});

/**
 * Rate limiting for user-specific actions (per user ID)
 */
const createUserSpecificLimiter = (windowMs, max, message) => {
  return rateLimit({
    windowMs,
    max,
    keyGenerator: (req) => {
      // Use user ID if authenticated, otherwise fall back to IP
      return req.user ? req.user._id.toString() : req.ip;
    },
    message: {
      success: false,
      message
    },
    standardHeaders: true,
    legacyHeaders: false,
    handler: (req, res) => {
      const identifier = req.user ? `User ID: ${req.user._id}` : `IP: ${req.ip}`;
      logger.warn(`User-specific rate limit exceeded for ${identifier}`);
      res.status(429).json({
        success: false,
        message
      });
    }
  });
};

/**
 * Rate limiting for mentor application submissions (per user)
 */
const mentorApplicationLimiter = createUserSpecificLimiter(
  24 * 60 * 60 * 1000, // 24 hours
  1, // 1 application per day per user
  'You can only submit one mentor application per day.'
);

/**
 * Rate limiting for password reset attempts (per user)
 */
const passwordResetLimiter = createUserSpecificLimiter(
  60 * 60 * 1000, // 1 hour
  3, // 3 attempts per hour per user
  'Too many password reset attempts. Please try again later.'
);

/**
 * Rate limiting for profile updates (per user)
 */
const profileUpdateLimiter = createUserSpecificLimiter(
  60 * 60 * 1000, // 1 hour
  10, // 10 updates per hour per user
  'Too many profile updates. Please try again later.'
);

/**
 * Rate limiting for monitoring endpoints
 * Very lenient to allow frequent monitoring requests
 */
const monitoringLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: process.env.NODE_ENV === 'production' ? 500 : 2000, // 500 in prod, 2000 in dev
  message: {
    success: false,
    message: 'Too many monitoring requests, please try again later.'
  },
  standardHeaders: true,
  legacyHeaders: false,
  handler: (req, res) => {
    logger.warn(`Monitoring rate limit exceeded for IP: ${req.ip}`);
    res.status(429).json({
      success: false,
      message: 'Too many monitoring requests, please try again later.'
    });
  }
});

/**
 * Helmet configuration for security headers
 */
const helmetConfig = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'", "https://fonts.googleapis.com"],
      fontSrc: ["'self'", "https://fonts.gstatic.com"],
      imgSrc: ["'self'", "data:", "https:", "blob:"],
      scriptSrc: ["'self'"],
      connectSrc: ["'self'", "https://discord.com", "https://discordapp.com"],
      frameSrc: ["'none'"],
      objectSrc: ["'none'"],
      upgradeInsecureRequests: [],
    },
  },
  crossOriginEmbedderPolicy: false, // Disable for Discord OAuth
  hsts: {
    maxAge: 31536000,
    includeSubDomains: true,
    preload: true
  }
});

/**
 * MongoDB injection protection
 */
const mongoSanitizeConfig = mongoSanitize({
  replaceWith: '_',
  onSanitize: ({ req, key }) => {
    logger.warn(`Potential NoSQL injection attempt detected from IP: ${req.ip}, key: ${key}`);
  }
});

/**
 * Input sanitization middleware
 */
const sanitizeInput = (req, res, next) => {
  // Sanitize request body
  if (req.body && typeof req.body === 'object') {
    sanitizeObject(req.body);
  }

  // Sanitize query parameters
  if (req.query && typeof req.query === 'object') {
    sanitizeObject(req.query);
  }

  // Sanitize URL parameters
  if (req.params && typeof req.params === 'object') {
    sanitizeObject(req.params);
  }

  next();
};

/**
 * Recursively sanitize object properties
 */
function sanitizeObject(obj) {
  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      if (typeof obj[key] === 'string') {
        // Remove potential script tags and dangerous characters
        obj[key] = obj[key]
          .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
          .replace(/javascript:/gi, '')
          .replace(/on\w+\s*=/gi, '')
          .trim();
      } else if (typeof obj[key] === 'object' && obj[key] !== null) {
        sanitizeObject(obj[key]);
      }
    }
  }
}

/**
 * Request logging middleware for security monitoring
 */
const securityLogger = (req, res, next) => {
  // Log suspicious patterns
  const suspiciousPatterns = [
    /\$where/i,
    /\$ne/i,
    /\$gt/i,
    /\$lt/i,
    /\$regex/i,
    /<script/i,
    /javascript:/i,
    /eval\(/i,
    /union.*select/i,
    /drop.*table/i
  ];

  const requestData = JSON.stringify({
    body: req.body,
    query: req.query,
    params: req.params
  });

  const foundSuspicious = suspiciousPatterns.some(pattern => pattern.test(requestData));

  if (foundSuspicious) {
    logger.warn(`Suspicious request detected from IP: ${req.ip}`, {
      method: req.method,
      url: req.url,
      userAgent: req.get('User-Agent'),
      body: req.body,
      query: req.query,
      params: req.params
    });
  }

  next();
};

/**
 * File upload security middleware
 */
const fileUploadSecurity = (req, res, next) => {
  if (req.file) {
    const allowedMimeTypes = [
      'image/jpeg',
      'image/png',
      'image/gif',
      'image/webp',
      'video/mp4',
      'video/webm',
      'audio/mpeg',
      'audio/wav',
      'application/pdf'
    ];

    if (!allowedMimeTypes.includes(req.file.mimetype)) {
      return res.status(400).json({
        success: false,
        message: 'File type not allowed'
      });
    }

    // Check file size (handled by multer config, but double-check)
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (req.file.size > maxSize) {
      return res.status(400).json({
        success: false,
        message: 'File size too large'
      });
    }

    // Log file upload for security monitoring
    logger.info(`File uploaded by user ${req.user?.id || 'unknown'}`, {
      filename: req.file.filename,
      mimetype: req.file.mimetype,
      size: req.file.size,
      ip: req.ip
    });
  }

  next();
};

module.exports = {
  generalLimiter,
  authLimiter,
  contentCreationLimiter,
  uploadLimiter,
  chatLimiter,
  mentorApplicationLimiter,
  passwordResetLimiter,
  profileUpdateLimiter,
  monitoringLimiter,
  createUserSpecificLimiter,
  helmetConfig,
  mongoSanitizeConfig,
  sanitizeInput,
  securityLogger,
  fileUploadSecurity
};

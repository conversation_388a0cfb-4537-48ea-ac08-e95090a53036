import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { MatCardModule } from '@angular/material/card';
import { MatTableModule } from '@angular/material/table';
import { MatPaginatorModule, PageEvent } from '@angular/material/paginator';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatSnackBarModule, MatSnackBar } from '@angular/material/snack-bar';
import { MatDialogModule, MatDialog } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSlideToggleModule } from '@angular/material/slide-toggle';
import { RouterModule } from '@angular/router';

import { AdminService, PaginatedResponse } from '../../../core/services/admin.service';
import { Content } from '../../../core/services/content.service';

@Component({
  selector: 'app-content-management',
  standalone: true,
  imports: [
    CommonModule,
    FormsModule,
    MatCardModule,
    MatTableModule,
    MatPaginatorModule,
    MatButtonModule,
    MatIconModule,
    MatInputModule,
    MatSelectModule,
    MatChipsModule,
    MatProgressSpinnerModule,
    MatSnackBarModule,
    MatDialogModule,
    MatFormFieldModule,
    MatSlideToggleModule,
    RouterModule
  ],
  templateUrl: './content-management.component.html',
  styleUrl: './content-management.component.scss'
})
export class ContentManagementComponent implements OnInit {
  content: Content[] = [];
  loading = true;
  error: string | null = null;

  // Pagination
  totalContent = 0;
  pageSize = 10;
  currentPage = 0;
  pageSizeOptions = [5, 10, 25, 50];

  // Filters
  searchTerm = '';
  categoryFilter = '';
  statusFilter = '';
  difficultyFilter = '';

  displayedColumns: string[] = ['title', 'creator', 'category', 'difficulty', 'status', 'stats', 'actions'];

  constructor(
    private adminService: AdminService,
    private snackBar: MatSnackBar,
    private dialog: MatDialog
  ) {}

  ngOnInit(): void {
    this.loadContent();
  }

  loadContent(): void {
    this.loading = true;
    this.error = null;

    const params = {
      page: this.currentPage + 1,
      limit: this.pageSize,
      search: this.searchTerm || undefined,
      category: this.categoryFilter || undefined,
      isPublished: this.statusFilter || undefined,
      difficulty: this.difficultyFilter || undefined
    };

    this.adminService.getAllContent(params).subscribe({
      next: (response: PaginatedResponse<Content>) => {
        this.content = response.data;
        this.totalContent = response.pagination.totalItems;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading content:', error);
        this.error = 'Failed to load content';
        this.loading = false;
      }
    });
  }

  onPageChange(event: PageEvent): void {
    this.currentPage = event.pageIndex;
    this.pageSize = event.pageSize;
    this.loadContent();
  }

  onSearch(): void {
    this.currentPage = 0;
    this.loadContent();
  }

  onFilterChange(): void {
    this.currentPage = 0;
    this.loadContent();
  }

  clearFilters(): void {
    this.searchTerm = '';
    this.categoryFilter = '';
    this.statusFilter = '';
    this.difficultyFilter = '';
    this.currentPage = 0;
    this.loadContent();
  }

  toggleContentStatus(content: Content): void {
    const newStatus = !content.isPublished;
    this.adminService.updateContentStatus(content._id, newStatus).subscribe({
      next: (updatedContent) => {
        const index = this.content.findIndex(c => c._id === content._id);
        if (index !== -1) {
          this.content[index] = updatedContent;
        }
        const action = newStatus ? 'published' : 'unpublished';
        this.snackBar.open(`Content ${action} successfully`, 'Close', { duration: 3000 });
      },
      error: (error) => {
        console.error('Error updating content status:', error);
        this.snackBar.open('Failed to update content status', 'Close', { duration: 3000 });
      }
    });
  }

  deleteContent(content: Content): void {
    if (confirm(`Are you sure you want to delete "${content.title}"? This action cannot be undone.`)) {
      this.adminService.deleteContent(content._id).subscribe({
        next: () => {
          this.snackBar.open('Content deleted successfully', 'Close', { duration: 3000 });
          this.loadContent(); // Refresh the list
        },
        error: (error) => {
          console.error('Error deleting content:', error);
          this.snackBar.open('Failed to delete content', 'Close', { duration: 3000 });
        }
      });
    }
  }

  getDifficultyColor(difficulty: string): string {
    switch (difficulty) {
      case 'beginner': return 'primary';
      case 'intermediate': return 'accent';
      case 'advanced': return 'warn';
      default: return 'primary';
    }
  }

  getStatusColor(isPublished: boolean): string {
    return isPublished ? 'primary' : 'warn';
  }

  formatDate(date: string): string {
    return new Date(date).toLocaleDateString();
  }
}

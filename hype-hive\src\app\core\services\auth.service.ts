import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { isPlatformBrowser } from '@angular/common';
import { HttpClient } from '@angular/common/http';
import { Router } from '@angular/router';
import { BehaviorSubject, Observable, of, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();
  private isAuthenticatedSubject = new BehaviorSubject<boolean>(false);
  public isAuthenticated$ = this.isAuthenticatedSubject.asObservable();
  private tokenKey = 'discord_token';

  constructor(
    private http: HttpClient,
    private router: Router,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.checkAuthStatus();
  }

  private isBrowser(): boolean {
    return isPlatformBrowser(this.platformId);
  }

  /**
   * Check if the user is already authenticated
   */
  private checkAuthStatus(): void {
    if (!this.isBrowser()) return;

    // Check for token in localStorage (backward compatibility)
    const token = localStorage.getItem(this.tokenKey);

    // Always try to get user profile to check both token and cookie auth
    this.getUserProfile().subscribe({
      next: (user) => {
        // User is authenticated (either via token or cookie)
        console.log('User authenticated on app startup:', user.discordUsername);
      },
      error: (error) => {
        // User is not authenticated, which is fine
        console.log('User not authenticated on app startup');
        this.isAuthenticatedSubject.next(false);
        this.currentUserSubject.next(null);
      }
    });
  }

  /**
   * Get the Discord OAuth URL for login (server-side callback)
   */
  getDiscordAuthUrl(returnUrl?: string): string {
    const clientId = environment.discord.clientId;
    const redirectUri = encodeURIComponent(environment.discord.redirectUri);
    const scope = encodeURIComponent('identify email');
    const state = returnUrl ? encodeURIComponent(returnUrl) : '';
    const stateParam = state ? `&state=${state}` : '';
    return `${environment.discord.apiEndpoint}/oauth2/authorize?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=code&scope=${scope}${stateParam}`;
  }

  /**
   * Handle the OAuth callback from Discord
   * @param code The authorization code from Discord
   */
  handleAuthCallback(code: string): Observable<User> {
    return this.http.post<any>(`${environment.apiUrl}/auth/discord`, { code })
      .pipe(
        tap(response => {
          if (response.success && response.token) {
            if (this.isBrowser()) {
              localStorage.setItem(this.tokenKey, response.token);
            }
            this.isAuthenticatedSubject.next(true);
            if (response.user) {
              this.currentUserSubject.next(response.user);
            }
          } else {
            throw new Error(response.message || 'Authentication failed');
          }
        }),
        map(response => response.user),
        catchError(error => {
          console.error('Authentication error:', error);
          return throwError(() => new Error(error.error?.message || 'Authentication failed'));
        })
      );
  }

  /**
   * Get the current user's profile
   */
  getUserProfile(): Observable<User> {
    if (!this.isBrowser()) {
      this.isAuthenticatedSubject.next(false);
      return throwError(() => new Error('Not authenticated'));
    }

    // Try to get token from localStorage for backward compatibility
    const token = localStorage.getItem(this.tokenKey);
    const headers: any = {};
    if (token) {
      headers['Authorization'] = `Bearer ${token}`;
    }

    // Make request with credentials to support both token and cookie auth
    return this.http.get<any>(`${environment.apiUrl}/auth/me`, {
      headers,
      withCredentials: true
    }).pipe(
      tap(response => {
        if (response.success && response.user) {
          this.currentUserSubject.next(response.user);
          this.isAuthenticatedSubject.next(true);
        } else {
          throw new Error(response.message || 'Failed to fetch user profile');
        }
      }),
      map(response => response.user),
      catchError(error => {
        console.error('Error fetching user profile:', error);
        // Don't automatically logout on 401 during initial check
        // Only set auth state to false
        this.isAuthenticatedSubject.next(false);
        this.currentUserSubject.next(null);
        return throwError(() => new Error(error.error?.message || 'Failed to fetch user profile'));
      })
    );
  }

  /**
   * Log the user out
   */
  logout(): void {
    // Call server logout endpoint to clear cookie
    this.http.post(`${environment.apiUrl}/auth/logout`, {}, { withCredentials: true })
      .subscribe({
        next: () => {
          console.log('Server logout successful');
        },
        error: (error) => {
          console.error('Server logout error:', error);
        }
      });

    // Clear local storage and update state
    if (this.isBrowser()) {
      localStorage.removeItem(this.tokenKey);
    }
    this.currentUserSubject.next(null);
    this.isAuthenticatedSubject.next(false);
    this.router.navigate(['/']);
  }

  /**
   * Get the current user
   */
  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  /**
   * Check if the user is authenticated
   */
  isAuthenticated(): boolean {
    return this.isAuthenticatedSubject.value;
  }

  /**
   * Get the current auth token
   */
  getToken(): string | null {
    if (!this.isBrowser()) return null;
    return localStorage.getItem(this.tokenKey);
  }

  /**
   * Update the current user in the subject
   */
  updateCurrentUser(user: User): void {
    this.currentUserSubject.next(user);
  }

  /**
   * Manually refresh authentication status
   * Useful after server-side redirects
   */
  refreshAuthStatus(): Observable<User | null> {
    return this.getUserProfile().pipe(
      catchError(() => {
        // If getUserProfile fails, user is not authenticated
        return of(null);
      })
    );
  }
}

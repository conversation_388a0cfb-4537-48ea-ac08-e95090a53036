const authService = require('../../src/services/authService');
const User = require('../../src/models/User');
const jwt = require('jsonwebtoken');

// Mock external dependencies
jest.mock('axios');
const axios = require('axios');

describe('Auth Service', () => {
  describe('exchangeCodeForToken', () => {
    test('should exchange Discord code for access token', async () => {
      const mockTokenResponse = {
        data: {
          access_token: 'mock_access_token',
          token_type: 'Bearer',
          expires_in: 3600,
          refresh_token: 'mock_refresh_token'
        }
      };

      axios.post.mockResolvedValueOnce(mockTokenResponse);

      const result = await authService.exchangeCodeForToken('test_code');

      expect(axios.post).toHaveBeenCalledWith(
        'https://discord.com/api/oauth2/token',
        expect.any(URLSearchParams),
        expect.objectContaining({
          headers: { 'Content-Type': 'application/x-www-form-urlencoded' }
        })
      );

      expect(result).toEqual(mockTokenResponse.data);
    });

    test('should handle Discord API errors', async () => {
      axios.post.mockRejectedValueOnce(new Error('Discord API Error'));

      await expect(authService.exchangeCodeForToken('invalid_code'))
        .rejects.toThrow('Discord API Error');
    });
  });

  describe('getDiscordUser', () => {
    test('should fetch Discord user data', async () => {
      const mockUserResponse = {
        data: {
          id: '123456789',
          username: 'testuser',
          email: '<EMAIL>',
          avatar: 'avatar_hash'
        }
      };

      axios.get.mockResolvedValueOnce(mockUserResponse);

      const result = await authService.getDiscordUser('mock_access_token');

      expect(axios.get).toHaveBeenCalledWith(
        'https://discord.com/api/v10/users/@me',
        expect.objectContaining({
          headers: { Authorization: 'Bearer mock_access_token' }
        })
      );

      expect(result).toEqual(mockUserResponse.data);
    });

    test('should handle unauthorized access', async () => {
      axios.get.mockRejectedValueOnce({
        response: { status: 401, data: { message: 'Unauthorized' } }
      });

      await expect(authService.getDiscordUser('invalid_token'))
        .rejects.toThrow();
    });
  });

  describe('createOrUpdateUser', () => {
    test('should create new user from Discord data', async () => {
      const discordUser = {
        id: '123456789',
        username: 'testuser',
        email: '<EMAIL>',
        avatar: 'avatar_hash'
      };

      const result = await authService.createOrUpdateUser(discordUser);

      expect(result).toBeDefined();
      expect(result.discordId).toBe(discordUser.id);
      expect(result.username).toBe(discordUser.username);
      expect(result.email).toBe(discordUser.email);
      expect(result.avatar).toContain(discordUser.avatar);

      // Verify user was saved to database
      const savedUser = await User.findOne({ discordId: discordUser.id });
      expect(savedUser).toBeDefined();
    });

    test('should update existing user', async () => {
      // Create existing user
      const existingUser = new User(global.testUtils.createTestUser());
      await existingUser.save();

      const discordUser = {
        id: existingUser.discordId,
        username: 'updated_username',
        email: '<EMAIL>',
        avatar: 'new_avatar_hash'
      };

      const result = await authService.createOrUpdateUser(discordUser);

      expect(result.username).toBe(discordUser.username);
      expect(result.email).toBe(discordUser.email);
      expect(result.avatar).toContain(discordUser.avatar);

      // Verify only one user exists
      const userCount = await User.countDocuments({ discordId: discordUser.id });
      expect(userCount).toBe(1);
    });

    test('should set default proficiencies for new users', async () => {
      const discordUser = {
        id: '987654321',
        username: 'newuser',
        email: '<EMAIL>',
        avatar: 'avatar_hash'
      };

      const result = await authService.createOrUpdateUser(discordUser);

      expect(result.proficiencies).toBeDefined();
      expect(result.proficiencies.streaming).toBe('beginner');
      expect(result.proficiencies.contentCreation).toBe('beginner');
      expect(result.proficiencies.communityBuilding).toBe('beginner');
    });
  });

  describe('generateJWT', () => {
    test('should generate valid JWT token', () => {
      const user = {
        _id: 'user_id_123',
        discordId: '123456789',
        username: 'testuser'
      };

      const token = authService.generateJWT(user);

      expect(token).toBeDefined();
      expect(typeof token).toBe('string');

      // Verify token can be decoded
      const decoded = jwt.verify(token, process.env.JWT_SECRET || 'test_secret');
      expect(decoded.userId).toBe(user._id);
      expect(decoded.discordId).toBe(user.discordId);
      expect(decoded.username).toBe(user.username);
    });

    test('should include expiration in token', () => {
      const user = {
        _id: 'user_id_123',
        discordId: '123456789',
        username: 'testuser'
      };

      const token = authService.generateJWT(user);
      const decoded = jwt.decode(token);

      expect(decoded.exp).toBeDefined();
      expect(decoded.iat).toBeDefined();
      expect(decoded.exp > decoded.iat).toBe(true);
    });
  });

  describe('verifyJWT', () => {
    test('should verify valid JWT token', () => {
      const user = {
        _id: 'user_id_123',
        discordId: '123456789',
        username: 'testuser'
      };

      const token = authService.generateJWT(user);
      const decoded = authService.verifyJWT(token);

      expect(decoded.userId).toBe(user._id);
      expect(decoded.discordId).toBe(user.discordId);
      expect(decoded.username).toBe(user.username);
    });

    test('should throw error for invalid token', () => {
      expect(() => {
        authService.verifyJWT('invalid_token');
      }).toThrow();
    });

    test('should throw error for expired token', () => {
      const expiredToken = jwt.sign(
        { userId: 'test' },
        process.env.JWT_SECRET || 'test_secret',
        { expiresIn: '-1h' }
      );

      expect(() => {
        authService.verifyJWT(expiredToken);
      }).toThrow();
    });
  });
});

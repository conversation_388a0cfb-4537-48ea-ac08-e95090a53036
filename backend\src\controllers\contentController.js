const contentService = require('../services/contentService');
const logger = require('../config/logger');

/**
 * Get all content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getAllContent = async (req, res) => {
  try {
    const result = await contentService.getAllContent(req.query);
    
    return res.status(200).json({
      success: true,
      data: result.content,
      pagination: result.pagination
    });
  } catch (error) {
    logger.error(`Get all content error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get content',
      error: error.message
    });
  }
};

/**
 * Get content by ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getContentById = async (req, res) => {
  try {
    const content = await contentService.getContentById(req.params.id);
    
    return res.status(200).json({
      success: true,
      data: content
    });
  } catch (error) {
    logger.error(`Get content by ID error: ${error.message}`);
    
    if (error.message === 'Content not found') {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to get content',
      error: error.message
    });
  }
};

/**
 * Create new content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const createContent = async (req, res) => {
  try {
    const content = await contentService.createContent(req.body, req.user._id);
    
    return res.status(201).json({
      success: true,
      data: content,
      message: 'Content created successfully'
    });
  } catch (error) {
    logger.error(`Create content error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to create content',
      error: error.message
    });
  }
};

/**
 * Update content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateContent = async (req, res) => {
  try {
    const content = await contentService.updateContent(
      req.params.id,
      req.body,
      req.user._id
    );
    
    return res.status(200).json({
      success: true,
      data: content,
      message: 'Content updated successfully'
    });
  } catch (error) {
    logger.error(`Update content error: ${error.message}`);
    
    if (error.message === 'Content not found') {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }
    
    if (error.message === 'Not authorized to update this content') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to update this content'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to update content',
      error: error.message
    });
  }
};

/**
 * Delete content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const deleteContent = async (req, res) => {
  try {
    await contentService.deleteContent(req.params.id, req.user._id);
    
    return res.status(200).json({
      success: true,
      message: 'Content deleted successfully'
    });
  } catch (error) {
    logger.error(`Delete content error: ${error.message}`);
    
    if (error.message === 'Content not found') {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }
    
    if (error.message === 'Not authorized to delete this content') {
      return res.status(403).json({
        success: false,
        message: 'Not authorized to delete this content'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to delete content',
      error: error.message
    });
  }
};

/**
 * Like or unlike content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const toggleLike = async (req, res) => {
  try {
    const content = await contentService.toggleLike(req.params.id, req.user._id);
    
    return res.status(200).json({
      success: true,
      data: {
        likes: content.likes,
        liked: content.likedBy.includes(req.user._id)
      },
      message: 'Like toggled successfully'
    });
  } catch (error) {
    logger.error(`Toggle like error: ${error.message}`);
    
    if (error.message === 'Content not found') {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to toggle like',
      error: error.message
    });
  }
};

/**
 * Add comment to content
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const addComment = async (req, res) => {
  try {
    const { text } = req.body;
    
    if (!text) {
      return res.status(400).json({
        success: false,
        message: 'Comment text is required'
      });
    }
    
    const content = await contentService.addComment(
      req.params.id,
      req.user._id,
      text
    );
    
    return res.status(201).json({
      success: true,
      data: content.comments[content.comments.length - 1],
      message: 'Comment added successfully'
    });
  } catch (error) {
    logger.error(`Add comment error: ${error.message}`);
    
    if (error.message === 'Content not found') {
      return res.status(404).json({
        success: false,
        message: 'Content not found'
      });
    }
    
    return res.status(500).json({
      success: false,
      message: 'Failed to add comment',
      error: error.message
    });
  }
};

module.exports = {
  getAllContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  toggleLike,
  addComment
};

import { Injectable } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { Observable } from 'rxjs';
import { map } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { User } from '../models/user.model';
import { Content } from './content.service';

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    currentPage: number;
    totalPages: number;
    totalItems: number;
    itemsPerPage: number;
  };
}

export interface UserStats {
  totalUsers: number;
  activeUsers: number;
  mentors: number;
  pendingApplications: number;
  newUsersThisMonth: number;
}

export interface ContentStats {
  totalContent: number;
  publishedContent: number;
  draftContent: number;
  totalViews: number;
  totalLikes: number;
}

export interface SystemStats {
  userStats: UserStats;
  contentStats: ContentStats;
  recentActivity: any[];
}

@Injectable({
  providedIn: 'root'
})
export class AdminService {
  private apiUrl = environment.apiUrl;

  constructor(private http: HttpClient) { }

  /**
   * Get system statistics for admin dashboard
   */
  getSystemStats(): Observable<SystemStats> {
    return this.http.get<any>(`${this.apiUrl}/admin/stats`).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get all users with pagination and filtering
   */
  getAllUsers(params: any = {}): Observable<PaginatedResponse<User>> {
    let httpParams = new HttpParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        httpParams = httpParams.append(key, params[key]);
      }
    });

    return this.http.get<any>(`${this.apiUrl}/users`, { params: httpParams }).pipe(
      map(response => ({
        data: response.data,
        pagination: response.pagination
      }))
    );
  }

  /**
   * Update user role
   */
  updateUserRole(userId: string, role: string): Observable<User> {
    return this.http.put<any>(`${this.apiUrl}/admin/users/${userId}/role`, { role }).pipe(
      map(response => response.data)
    );
  }

  /**
   * Toggle user active status
   */
  toggleUserStatus(userId: string, isActive: boolean): Observable<User> {
    return this.http.put<any>(`${this.apiUrl}/admin/users/${userId}/status`, { isActive }).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get pending mentor applications
   */
  getPendingMentorApplications(): Observable<User[]> {
    return this.http.get<any>(`${this.apiUrl}/admin/mentor-applications`).pipe(
      map(response => response.data)
    );
  }

  /**
   * Review mentor application
   */
  reviewMentorApplication(userId: string, status: 'approved' | 'rejected', notes?: string): Observable<User> {
    return this.http.put<any>(`${this.apiUrl}/users/${userId}/mentor/review`, { 
      status, 
      notes 
    }).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get all content with admin privileges
   */
  getAllContent(params: any = {}): Observable<PaginatedResponse<Content>> {
    let httpParams = new HttpParams();
    Object.keys(params).forEach(key => {
      if (params[key] !== undefined && params[key] !== null) {
        httpParams = httpParams.append(key, params[key]);
      }
    });

    return this.http.get<any>(`${this.apiUrl}/admin/content`, { params: httpParams }).pipe(
      map(response => ({
        data: response.data,
        pagination: response.pagination
      }))
    );
  }

  /**
   * Update content status (publish/unpublish)
   */
  updateContentStatus(contentId: string, isPublished: boolean): Observable<Content> {
    return this.http.put<any>(`${this.apiUrl}/admin/content/${contentId}/status`, { isPublished }).pipe(
      map(response => response.data)
    );
  }

  /**
   * Delete content (admin only)
   */
  deleteContent(contentId: string): Observable<void> {
    return this.http.delete<any>(`${this.apiUrl}/admin/content/${contentId}`).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get user activity logs
   */
  getUserActivityLogs(userId?: string, limit: number = 50): Observable<any[]> {
    let httpParams = new HttpParams().set('limit', limit.toString());
    if (userId) {
      httpParams = httpParams.set('userId', userId);
    }

    return this.http.get<any>(`${this.apiUrl}/admin/activity-logs`, { params: httpParams }).pipe(
      map(response => response.data)
    );
  }

  /**
   * Get system health status
   */
  getSystemHealth(): Observable<any> {
    return this.http.get<any>(`${this.apiUrl}/admin/health`).pipe(
      map(response => response.data)
    );
  }
}

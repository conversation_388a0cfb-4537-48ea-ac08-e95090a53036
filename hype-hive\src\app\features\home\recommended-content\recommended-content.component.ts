import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

import { RecommendationService } from '../../../core/services/recommendation.service';
import { Content } from '../../../core/services/content.service';
import { AuthService } from '../../../core/services/auth.service';

@Component({
  selector: 'app-recommended-content',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatProgressSpinnerModule
  ],
  templateUrl: './recommended-content.component.html',
  styleUrls: ['./recommended-content.component.scss']
})
export class RecommendedContentComponent implements OnInit {
  recommendedContent: Content[] = [];
  isLoading = true;
  errorMessage = '';
  isAuthenticated = false;
  
  constructor(
    private recommendationService: RecommendationService,
    private authService: AuthService
  ) {}
  
  ngOnInit(): void {
    this.isAuthenticated = this.authService.isAuthenticated();
    this.loadRecommendedContent();
  }
  
  /**
   * Load recommended content
   */
  loadRecommendedContent(): void {
    this.isLoading = true;
    this.errorMessage = '';
    
    this.recommendationService.getPersonalizedRecommendations().subscribe({
      next: (content) => {
        this.recommendedContent = content;
        this.isLoading = false;
      },
      error: (error) => {
        this.errorMessage = 'Failed to load recommendations. Please try again.';
        this.isLoading = false;
        console.error('Error loading recommendations:', error);
      }
    });
  }
  
  /**
   * Track content view
   */
  trackContentView(contentId: string): void {
    this.recommendationService.trackContentInteraction(contentId, 'view').subscribe();
  }
  
  /**
   * Get difficulty class
   */
  getDifficultyClass(difficulty: string): string {
    switch (difficulty) {
      case 'beginner':
        return 'difficulty-beginner';
      case 'intermediate':
        return 'difficulty-intermediate';
      case 'advanced':
        return 'difficulty-advanced';
      default:
        return '';
    }
  }
}

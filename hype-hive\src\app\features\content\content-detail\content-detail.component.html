<div class="content-detail-container">
  <!-- Loading State -->
  <div class="loading-container" *ngIf="isLoading">
    <mat-spinner diameter="50"></mat-spinner>
    <p>Loading content...</p>
  </div>

  <!-- Content Display -->
  <div class="content-detail" *ngIf="!isLoading && content">
    <!-- Header with back button -->
    <div class="content-header">
      <button mat-icon-button (click)="goBack()" class="back-button">
        <mat-icon>arrow_back</mat-icon>
      </button>
      <div class="header-info">
        <h1>{{ content.title }}</h1>
        <div class="content-meta">
          <mat-chip class="category-chip">{{ content.category }}</mat-chip>
          <mat-chip [class]="getDifficultyClass(content.difficulty)" class="difficulty-chip">
            {{ content.difficulty }}
          </mat-chip>
          <span class="views">
            <mat-icon>visibility</mat-icon>
            {{ content.views }} views
          </span>
          <span class="created-date">
            {{ content.createdAt | date:'mediumDate' }}
          </span>
        </div>
      </div>
    </div>

    <mat-divider></mat-divider>

    <!-- Video/Embed Section -->
    <div class="video-section" *ngIf="content.embedUrl || content.videoUrl">
      <mat-card class="video-card">
        <!-- Embedded Video (YouTube, etc.) -->
        <div class="embed-container" *ngIf="safeEmbedUrl">
          <iframe
            [src]="safeEmbedUrl"
            frameborder="0"
            allowfullscreen
            class="embed-video">
          </iframe>
        </div>

        <!-- Direct Video File -->
        <div class="video-container" *ngIf="content.videoUrl && !content.embedUrl">
          <video controls class="direct-video">
            <source [src]="content.videoUrl" type="video/mp4">
            Your browser does not support the video tag.
          </video>
        </div>
      </mat-card>
    </div>

    <!-- Thumbnail Section (if no video) -->
    <div class="thumbnail-section" *ngIf="!content.embedUrl && !content.videoUrl && content.thumbnailUrl">
      <mat-card class="thumbnail-card">
        <img [src]="content.thumbnailUrl" [alt]="content.title" class="content-thumbnail">
      </mat-card>
    </div>

    <!-- Content Information -->
    <div class="content-info">
      <mat-card class="info-card">
        <mat-card-header>
          <div class="creator-info" *ngIf="content.createdBy">
            <img
              [src]="content.createdBy.discordAvatar || '/assets/images/default-avatar.png'"
              [alt]="content.createdBy.discordUsername"
              class="creator-avatar">
            <div class="creator-details">
              <h3>{{ content.createdBy.discordUsername }}</h3>
              <span class="mentor-badge" *ngIf="content.createdBy.isMentor">
                <mat-icon>verified</mat-icon>
                Mentor
              </span>
            </div>
          </div>

          <div class="action-buttons">
            <button
              mat-raised-button
              [color]="isLiked ? 'accent' : 'primary'"
              (click)="toggleLike()"
              class="like-button">
              <mat-icon>{{ isLiked ? 'thumb_up' : 'thumb_up_off_alt' }}</mat-icon>
              {{ content.likes }} {{ content.likes === 1 ? 'Like' : 'Likes' }}
            </button>
          </div>
        </mat-card-header>

        <mat-card-content>
          <div class="description">
            <h2>Description</h2>
            <p>{{ content.description }}</p>
          </div>

          <!-- Tags -->
          <div class="tags-section" *ngIf="content.tags && content.tags.length > 0">
            <h3>Tags</h3>
            <div class="tags-container">
              <mat-chip *ngFor="let tag of content.tags" class="tag-chip">
                {{ tag }}
              </mat-chip>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Comments Section (placeholder for future implementation) -->
    <div class="comments-section">
      <mat-card class="comments-card">
        <mat-card-header>
          <h2>Comments</h2>
        </mat-card-header>
        <mat-card-content>
          <div class="no-comments" *ngIf="!content.comments || content.comments.length === 0">
            <mat-icon>comment</mat-icon>
            <p>No comments yet. Be the first to comment!</p>
          </div>

          <!-- Comments will be implemented later -->
          <div class="comments-list" *ngIf="content.comments && content.comments.length > 0">
            <div class="comment" *ngFor="let comment of content.comments">
              <div class="comment-header">
                <img
                  [src]="comment.user?.discordAvatar || '/assets/images/default-avatar.png'"
                  [alt]="comment.user?.discordUsername"
                  class="comment-avatar">
                <div class="comment-info">
                  <span class="comment-author">{{ comment.user?.discordUsername }}</span>
                  <span class="comment-date">{{ comment.createdAt | date:'short' }}</span>
                </div>
              </div>
              <p class="comment-text">{{ comment.text }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Error State -->
  <div class="error-container" *ngIf="!isLoading && !content">
    <mat-icon>error_outline</mat-icon>
    <h2>Content Not Found</h2>
    <p>The content you're looking for doesn't exist or has been removed.</p>
    <button mat-raised-button color="primary" (click)="goBack()">
      Back to Content
    </button>
  </div>
</div>

<div class="admin-dashboard">
  <div class="dashboard-header">
    <h1>Admin Dashboard</h1>
    <button mat-raised-button color="primary" (click)="refresh()" [disabled]="loading">
      <mat-icon>refresh</mat-icon>
      Refresh
    </button>
  </div>

  <!-- Loading State -->
  <div *ngIf="loading" class="loading-container">
    <mat-spinner></mat-spinner>
    <p>Loading dashboard data...</p>
  </div>

  <!-- Error State -->
  <div *ngIf="error && !loading" class="error-container">
    <mat-icon color="warn">error</mat-icon>
    <p>{{ error }}</p>
    <button mat-button color="primary" (click)="refresh()">Try Again</button>
  </div>

  <!-- Dashboard Content -->
  <div *ngIf="!loading && !error" class="dashboard-content">

    <!-- Statistics Cards -->
    <div class="stats-grid" *ngIf="stats">
      <mat-card class="stat-card">
        <mat-card-header>
          <mat-card-title>Users</mat-card-title>
          <mat-icon>people</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.userStats.totalUsers }}</div>
          <div class="stat-details">
            <span>{{ stats.userStats.activeUsers }} active</span>
            <span>{{ stats.userStats.newUsersThisMonth }} new this month</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-card-title>Mentors</mat-card-title>
          <mat-icon>school</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.userStats.mentors }}</div>
          <div class="stat-details">
            <span>{{ stats.userStats.pendingApplications }} pending applications</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-card-title>Content</mat-card-title>
          <mat-icon>library_books</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.contentStats.totalContent }}</div>
          <div class="stat-details">
            <span>{{ stats.contentStats.publishedContent }} published</span>
            <span>{{ stats.contentStats.totalViews }} total views</span>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-header>
          <mat-card-title>Engagement</mat-card-title>
          <mat-icon>favorite</mat-icon>
        </mat-card-header>
        <mat-card-content>
          <div class="stat-number">{{ stats.contentStats.totalLikes }}</div>
          <div class="stat-details">
            <span>Total likes across all content</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Quick Actions -->
    <div class="quick-actions">
      <h2>Quick Actions</h2>
      <div class="action-buttons">
        <button mat-raised-button color="primary" routerLink="/admin/users">
          <mat-icon>people</mat-icon>
          Manage Users
        </button>
        <button mat-raised-button color="primary" routerLink="/admin/content">
          <mat-icon>library_books</mat-icon>
          Manage Content
        </button>
        <button mat-raised-button color="accent" routerLink="/admin/mentor-applications">
          <mat-icon>assignment</mat-icon>
          Review Applications
        </button>
      </div>
    </div>

    <!-- Pending Mentor Applications -->
    <mat-card *ngIf="pendingApplications.length > 0" class="pending-applications">
      <mat-card-header>
        <mat-card-title>Pending Mentor Applications</mat-card-title>
        <mat-card-subtitle>{{ pendingApplications.length }} applications awaiting review</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <div class="application-list">
          <div *ngFor="let application of pendingApplications" class="application-item">
            <div class="user-info">
              <img [src]="application.discordAvatar" [alt]="application.discordUsername" class="avatar">
              <div class="user-details">
                <h4>{{ application.discordUsername }}</h4>
                <p>{{ application.email }}</p>
                <p class="bio">{{ application.bio || 'No bio provided' }}</p>
              </div>
            </div>
            <div class="application-actions">
              <button mat-button color="primary" (click)="approveMentorApplication(application.id)">
                <mat-icon>check</mat-icon>
                Approve
              </button>
              <button mat-button color="warn" (click)="rejectMentorApplication(application.id)">
                <mat-icon>close</mat-icon>
                Reject
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Recent Users -->
    <mat-card class="recent-users">
      <mat-card-header>
        <mat-card-title>Recent Users</mat-card-title>
        <mat-card-subtitle>Latest registered users</mat-card-subtitle>
      </mat-card-header>
      <mat-card-content>
        <table mat-table [dataSource]="recentUsers" class="users-table">
          <ng-container matColumnDef="username">
            <th mat-header-cell *matHeaderCellDef>Username</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-cell">
                <img [src]="user.discordAvatar" [alt]="user.discordUsername" class="avatar-small">
                {{ user.discordUsername }}
              </div>
            </td>
          </ng-container>

          <ng-container matColumnDef="email">
            <th mat-header-cell *matHeaderCellDef>Email</th>
            <td mat-cell *matCellDef="let user">{{ user.email }}</td>
          </ng-container>

          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef>Role</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip [color]="user.role === 'admin' ? 'warn' : user.role === 'mentor' ? 'accent' : 'primary'">
                {{ user.role }}
              </mat-chip>
            </td>
          </ng-container>

          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Status</th>
            <td mat-cell *matCellDef="let user">
              <mat-chip [color]="user.isActive ? 'primary' : 'warn'">
                {{ user.isActive ? 'Active' : 'Inactive' }}
              </mat-chip>
            </td>
          </ng-container>

          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <button mat-icon-button [color]="user.isActive ? 'warn' : 'primary'"
                      (click)="toggleUserStatus(user)"
                      [matTooltip]="user.isActive ? 'Deactivate User' : 'Activate User'">
                <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>
              </button>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="displayedColumns"></tr>
          <tr mat-row *matRowDef="let row; columns: displayedColumns;"></tr>
        </table>
      </mat-card-content>
    </mat-card>
  </div>
</div>

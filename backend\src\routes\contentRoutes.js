const express = require('express');
const {
  getAllContent,
  getContentById,
  createContent,
  updateContent,
  deleteContent,
  toggleLike,
  addComment
} = require('../controllers/contentController');
const { protect, authorize } = require('../middleware/auth');
const { contentValidation, commonValidations } = require('../middleware/validation');
const { contentCreationLimiter } = require('../middleware/security');

const router = express.Router();

// GET /api/content - Get all content
router.get('/', [commonValidations.page, commonValidations.limit], getAllContent);

// GET /api/content/:id - Get content by ID
router.get('/:id', contentValidation.getContentById, getContentById);

// POST /api/content - Create new content (mentor only)
router.post('/', contentValidation.createContent, protect, authorize('mentor', 'admin'), contentCreationLimiter, createContent);

// PUT /api/content/:id - Update content
router.put('/:id', contentValidation.updateContent, protect, updateContent);

// DELETE /api/content/:id - Delete content
router.delete('/:id', protect, [commonValidations.mongoId], deleteContent);

// POST /api/content/:id/like - Like or unlike content
router.post('/:id/like', protect, [commonValidations.mongoId], toggleLike);

// POST /api/content/:id/comment - Add comment to content
router.post('/:id/comment', protect, contentValidation.addComment, addComment);

module.exports = router;

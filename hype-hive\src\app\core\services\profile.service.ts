import { Injectable } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, map, tap } from 'rxjs/operators';
import { environment } from '../../../environments/environment';
import { User } from '../models/user.model';

@Injectable({
  providedIn: 'root'
})
export class ProfileService {
  private apiUrl = `${environment.apiUrl}/users`;
  
  constructor(private http: HttpClient) { }
  
  /**
   * Get user profile by ID
   * @param userId User ID
   */
  getUserProfile(userId: string): Observable<User> {
    return this.http.get<any>(`${this.apiUrl}/${userId}`)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to get user profile');
          }
        }),
        catchError(error => {
          console.error('Error getting user profile:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to get user profile'));
        })
      );
  }
  
  /**
   * Update user profile
   * @param userId User ID
   * @param profileData Profile data to update
   */
  updateUserProfile(userId: string, profileData: Partial<User>): Observable<User> {
    return this.http.put<any>(`${this.apiUrl}/${userId}`, profileData)
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to update profile');
          }
        }),
        catchError(error => {
          console.error('Error updating profile:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to update profile'));
        })
      );
  }
  
  /**
   * Apply for mentor status
   */
  applyForMentor(): Observable<User> {
    return this.http.post<any>(`${this.apiUrl}/mentor/apply`, {})
      .pipe(
        map(response => {
          if (response.success) {
            return response.data;
          } else {
            throw new Error(response.message || 'Failed to apply for mentor');
          }
        }),
        catchError(error => {
          console.error('Error applying for mentor:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to apply for mentor'));
        })
      );
  }
  
  /**
   * Get all mentors
   * @param query Query parameters
   */
  getMentors(query: any = {}): Observable<{ mentors: User[], pagination: any }> {
    // Build query string
    const queryParams = new URLSearchParams();
    Object.keys(query).forEach(key => {
      if (query[key] !== undefined && query[key] !== null) {
        queryParams.append(key, query[key]);
      }
    });
    
    const url = `${this.apiUrl}/mentors?${queryParams.toString()}`;
    
    return this.http.get<any>(url)
      .pipe(
        map(response => {
          if (response.success) {
            return {
              mentors: response.data,
              pagination: response.pagination
            };
          } else {
            throw new Error(response.message || 'Failed to get mentors');
          }
        }),
        catchError(error => {
          console.error('Error getting mentors:', error);
          return throwError(() => new Error(error.error?.message || 'Failed to get mentors'));
        })
      );
  }
}

import { NgModule } from '@angular/core';
import { RouterModule, Routes } from '@angular/router';
import { MentorSearchComponent } from './mentor-search/mentor-search.component';
import { MentorDetailComponent } from './mentor-detail/mentor-detail.component';
import { MentorApplicationComponent } from './mentor-application/mentor-application.component';
import { AuthGuard } from '../../core/guards/auth.guard';

const routes: Routes = [
  {
    path: '',
    component: MentorSearchComponent
  },
  {
    path: 'apply',
    component: MentorApplicationComponent,
    canActivate: [AuthGuard]
  },
  {
    path: ':id',
    component: MentorDetailComponent
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})
export class MentorsRoutingModule { }

# HypeHive Discord Bot Deployment Script (PowerShell)
# This script handles deployment of the Discord bot to various environments

param(
    [Parameter(Position=0)]
    [string]$Command = "help"
)

# Configuration
$ScriptDir = Split-Path -Parent $MyInvocation.MyCommand.Path
$ProjectName = "hive-discord-bot"
$ImageName = "hype-hive/discord-bot"
$ContainerName = "hive-discord-bot"

# Functions
function Write-Info {
    param([string]$Message)
    Write-Host "[INFO] $Message" -ForegroundColor Blue
}

function Write-Success {
    param([string]$Message)
    Write-Host "[SUCCESS] $Message" -ForegroundColor Green
}

function Write-Warning {
    param([string]$Message)
    Write-Host "[WARNING] $Message" -ForegroundColor Yellow
}

function Write-Error {
    param([string]$Message)
    Write-Host "[ERROR] $Message" -ForegroundColor Red
}

function Check-Requirements {
    Write-Info "Checking deployment requirements..."
    
    # Check if Docker is installed
    try {
        $dockerVersion = docker --version
        Write-Info "Docker found: $dockerVersion"
    }
    catch {
        Write-Error "Docker is not installed. Please install Docker Desktop first."
        exit 1
    }
    
    # Check if Docker Compose is installed
    try {
        $composeVersion = docker-compose --version
        Write-Info "Docker Compose found: $composeVersion"
    }
    catch {
        Write-Error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    }
    
    # Check if .env file exists
    $envFile = Join-Path $ScriptDir ".env"
    if (-not (Test-Path $envFile)) {
        Write-Error ".env file not found. Please create one based on .env.example"
        exit 1
    }
    
    # Check if required environment variables are set
    $envContent = Get-Content $envFile
    $hasToken = $envContent | Where-Object { $_ -match "^DISCORD_BOT_TOKEN=" -and $_ -notmatch "^DISCORD_BOT_TOKEN=$" }
    $hasGuildId = $envContent | Where-Object { $_ -match "^DISCORD_GUILD_ID=" -and $_ -notmatch "^DISCORD_GUILD_ID=$" }
    
    if (-not $hasToken) {
        Write-Error "DISCORD_BOT_TOKEN is not set in .env file"
        exit 1
    }
    
    if (-not $hasGuildId) {
        Write-Error "DISCORD_GUILD_ID is not set in .env file"
        exit 1
    }
    
    Write-Success "All requirements met"
}

function Build-Image {
    Write-Info "Building Docker image..."
    
    Set-Location $ScriptDir
    docker build -t "${ImageName}:latest" .
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Docker image built successfully"
    } else {
        Write-Error "Failed to build Docker image"
        exit 1
    }
}

function Deploy-Local {
    Write-Info "Deploying Discord bot locally..."
    
    # Stop existing container if running
    $existingContainer = docker ps -q -f name=$ContainerName
    if ($existingContainer) {
        Write-Info "Stopping existing container..."
        docker stop $ContainerName
        docker rm $ContainerName
    }
    
    # Create necessary directories
    $logsDir = Join-Path $ScriptDir "logs"
    $dataDir = Join-Path $ScriptDir "data"
    
    if (-not (Test-Path $logsDir)) {
        New-Item -ItemType Directory -Path $logsDir -Force | Out-Null
    }
    
    if (-not (Test-Path $dataDir)) {
        New-Item -ItemType Directory -Path $dataDir -Force | Out-Null
    }
    
    # Run the container
    $envFile = Join-Path $ScriptDir ".env"
    docker run -d `
        --name $ContainerName `
        --restart unless-stopped `
        --env-file $envFile `
        -v "${logsDir}:/app/logs" `
        -v "${dataDir}:/app/data" `
        "${ImageName}:latest"
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Discord bot deployed locally"
    } else {
        Write-Error "Failed to deploy Discord bot"
        exit 1
    }
}

function Deploy-Compose {
    Write-Info "Deploying with Docker Compose..."
    
    Set-Location $ScriptDir
    docker-compose down
    docker-compose up -d --build
    
    if ($LASTEXITCODE -eq 0) {
        Write-Success "Discord bot deployed with Docker Compose"
    } else {
        Write-Error "Failed to deploy with Docker Compose"
        exit 1
    }
}

function Check-Health {
    Write-Info "Checking bot health..."
    
    # Wait for container to start
    Start-Sleep -Seconds 10
    
    # Check if container is running
    $runningContainer = docker ps -q -f name=$ContainerName
    if ($runningContainer) {
        Write-Success "Container is running"
        
        # Check logs for successful startup
        $logs = docker logs $ContainerName 2>&1
        if ($logs -match "Bot is ready") {
            Write-Success "Bot started successfully"
        } else {
            Write-Warning "Bot may not have started properly. Check logs:"
            docker logs $ContainerName --tail 20
        }
    } else {
        Write-Error "Container is not running"
        docker logs $ContainerName --tail 20
        exit 1
    }
}

function Show-Logs {
    Write-Info "Showing bot logs..."
    docker logs -f $ContainerName
}

function Stop-Bot {
    Write-Info "Stopping Discord bot..."
    
    $runningContainer = docker ps -q -f name=$ContainerName
    if ($runningContainer) {
        docker stop $ContainerName
        docker rm $ContainerName
        Write-Success "Bot stopped"
    } else {
        Write-Warning "Bot is not running"
    }
}

function Show-Status {
    Write-Info "Bot status:"
    
    $runningContainer = docker ps -q -f name=$ContainerName
    if ($runningContainer) {
        Write-Host "Status: Running" -ForegroundColor Green
        docker ps -f name=$ContainerName --format "table {{.Names}}`t{{.Status}}`t{{.Ports}}"
    } else {
        Write-Host "Status: Stopped" -ForegroundColor Red
    }
}

function Show-Help {
    Write-Host "HypeHive Discord Bot Deployment Script"
    Write-Host ""
    Write-Host "Usage: .\deploy.ps1 [COMMAND]"
    Write-Host ""
    Write-Host "Commands:"
    Write-Host "  build       Build Docker image"
    Write-Host "  deploy      Deploy bot locally with Docker"
    Write-Host "  compose     Deploy with Docker Compose"
    Write-Host "  health      Check bot health"
    Write-Host "  logs        Show bot logs"
    Write-Host "  stop        Stop the bot"
    Write-Host "  status      Show bot status"
    Write-Host "  help        Show this help message"
    Write-Host ""
    Write-Host "Examples:"
    Write-Host "  .\deploy.ps1 build; .\deploy.ps1 deploy    # Build and deploy locally"
    Write-Host "  .\deploy.ps1 compose                       # Deploy with Docker Compose"
    Write-Host "  .\deploy.ps1 logs                          # View logs"
}

# Main script logic
switch ($Command.ToLower()) {
    "build" {
        Check-Requirements
        Build-Image
    }
    "deploy" {
        Check-Requirements
        Build-Image
        Deploy-Local
        Check-Health
    }
    "compose" {
        Check-Requirements
        Deploy-Compose
        Check-Health
    }
    "health" {
        Check-Health
    }
    "logs" {
        Show-Logs
    }
    "stop" {
        Stop-Bot
    }
    "status" {
        Show-Status
    }
    default {
        Show-Help
    }
}

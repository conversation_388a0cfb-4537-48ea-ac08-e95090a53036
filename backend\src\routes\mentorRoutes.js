const express = require('express');
const router = express.Router();
const Mentor = require('../models/Mentor');
const MentorApplication = require('../models/MentorApplication');
const { body } = require('express-validator');
const { handleValidationErrors, commonValidations } = require('../middleware/validation');
const { protect, authorize } = require('../middleware/auth');
const logger = require('../config/logger');
const mentorController = require('../controllers/mentorController');

// GET /api/mentors - List all mentors
router.get('/', [commonValidations.page, commonValidations.limit, handleValidationErrors], async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const mentors = await Mentor.find()
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Mentor.countDocuments();

    res.json({
      success: true,
      data: mentors,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      }
    });
  } catch (err) {
    logger.error('Error fetching mentors:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// POST /api/mentors - Add or update a mentor (admin only)
router.post('/', protect, authorize('admin'), [
  body('discord_id')
    .notEmpty()
    .isString()
    .isLength({ min: 17, max: 19 })
    .matches(/^\d+$/)
    .withMessage('Discord ID must be a valid snowflake ID'),
  body('name')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .escape()
    .withMessage('Name must be 2-50 characters'),
  body('proficiencies')
    .optional()
    .isArray()
    .withMessage('Proficiencies must be an array'),
  body('proficiencies.*')
    .isIn(['streaming', 'contentCreation', 'communityBuilding', 'marketing', 'technical'])
    .withMessage('Invalid proficiency area'),
  handleValidationErrors
], async (req, res) => {
  try {
    const { discord_id, name, proficiencies = [] } = req.body;

    let mentor = await Mentor.findOne({ discord_id });
    if (!mentor) {
      mentor = new Mentor({ discord_id, name, proficiencies });
      logger.info(`New mentor created: ${name} (${discord_id})`);
    } else {
      mentor.name = name;
      mentor.proficiencies = proficiencies;
      logger.info(`Mentor updated: ${name} (${discord_id})`);
    }

    await mentor.save();

    res.json({
      success: true,
      data: mentor
    });
  } catch (err) {
    logger.error('Error creating/updating mentor:', err);
    res.status(500).json({
      success: false,
      message: 'Server error'
    });
  }
});

// DELETE /api/mentors/:discord_id - Remove a mentor
router.delete('/:discord_id', async (req, res) => {
  const { discord_id } = req.params;
  try {
    const mentor = await Mentor.findOneAndDelete({ discord_id });
    if (mentor) return res.json({ success: true });
    res.status(404).json({ error: 'Mentor not found' });
  } catch (err) {
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/mentors/:discord_id/proficiencies - Add/remove proficiency
router.post('/:discord_id/proficiencies', async (req, res) => {
  const { discord_id } = req.params;
  const { action, proficiency } = req.body;
  if (!proficiency) return res.status(400).json({ error: 'proficiency required' });
  try {
    const mentor = await Mentor.findOne({ discord_id });
    if (!mentor) return res.status(404).json({ error: 'Mentor not found' });
    if (action === 'add') {
      if (!mentor.proficiencies.includes(proficiency)) {
        mentor.proficiencies.push(proficiency);
      }
    } else if (action === 'remove') {
      mentor.proficiencies = mentor.proficiencies.filter(p => p !== proficiency);
    } else {
      return res.status(400).json({ error: 'Invalid action' });
    }
    await mentor.save();
    res.json(mentor);
  } catch (err) {
    res.status(500).json({ error: 'Server error' });
  }
});

// POST /api/mentors/apply - Regular user submits mentor application
router.post('/apply', protect, [
  body('name')
    .notEmpty()
    .isString()
    .trim()
    .isLength({ min: 2, max: 50 })
    .escape()
    .withMessage('Name must be 2-50 characters'),
  body('discord_id')
    .notEmpty()
    .isString()
    .isLength({ min: 17, max: 19 })
    .matches(/^[0-9]+$/)
    .withMessage('Discord ID must be a valid snowflake ID'),
  body('proficiencies')
    .optional()
    .isArray()
    .withMessage('Proficiencies must be an array'),
  body('proficiencies.*')
    .isIn(['streaming', 'contentCreation', 'communityBuilding', 'marketing', 'technical'])
    .withMessage('Invalid proficiency area'),
  handleValidationErrors
], mentorController.applyForMentor);

// Admin endpoints for mentor applications
router.get('/applications', protect, authorize('admin'), mentorController.listApplications);
router.post('/applications/:id/approve', protect, authorize('admin'), mentorController.approveApplication);
router.post('/applications/:id/reject', protect, authorize('admin'), mentorController.rejectApplication);

module.exports = router;

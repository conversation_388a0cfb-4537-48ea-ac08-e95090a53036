import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

import { RecommendedContentComponent } from '../recommended-content/recommended-content.component';
import { MentorCardComponent } from '../../mentors/mentor-card/mentor-card.component';
import { AuthService } from '../../../core/services/auth.service';
import { RecommendationService } from '../../../core/services/recommendation.service';
import { User, InstructionCategory } from '../../../core/models/user.model';

@Component({
  selector: 'app-home',
  standalone: true,
  imports: [
    CommonModule,
    RouterModule,
    MatButtonModule,
    MatIconModule,
    RecommendedContentComponent,
    MentorCardComponent
  ],
  templateUrl: './home.component.html',
  styleUrl: './home.component.scss'
})
export class HomeComponent implements OnInit {
  isAuthenticated = false;
  categories = Object.values(InstructionCategory);
  recommendedMentors: User[] = [];

  constructor(
    private authService: AuthService,
    private recommendationService: RecommendationService
  ) {}

  ngOnInit(): void {
    this.isAuthenticated = this.authService.isAuthenticated();
    this.loadRecommendedMentors();
  }

  /**
   * Load recommended mentors
   */
  loadRecommendedMentors(): void {
    this.recommendationService.getRecommendedMentors().subscribe({
      next: (mentors) => {
        this.recommendedMentors = mentors.slice(0, 3); // Show only 3 mentors
      },
      error: (error) => {
        console.error('Error loading recommended mentors:', error);
      }
    });
  }

  /**
   * Get icon for category
   */
  getCategoryIcon(category: string): string {
    switch (category) {
      case 'Account Setup':
        return 'account_circle';
      case 'Bots':
        return 'smart_toy';
      case 'Networking':
        return 'share';
      case 'Emotes':
        return 'emoji_emotions';
      case 'Streaming Platforms':
        return 'live_tv';
      case 'Affiliate Roles':
        return 'monetization_on';
      case 'Console Help':
        return 'sports_esports';
      case 'PC Help':
        return 'computer';
      default:
        return 'category';
    }
  }
}

const User = require('../models/User');
const logger = require('../config/logger');

/**
 * Health check for Discord bot
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getBotHealth = async (req, res) => {
  try {
    // Check database connection by counting users
    const userCount = await User.countDocuments();
    const mentorCount = await User.countDocuments({ isMentor: true });
    
    return res.status(200).json({
      success: true,
      status: 'healthy',
      data: {
        timestamp: new Date().toISOString(),
        database: 'connected',
        userCount,
        mentorCount
      }
    });
  } catch (error) {
    logger.error(`Discord bot health check error: ${error.message}`);
    return res.status(500).json({
      success: false,
      status: 'unhealthy',
      message: 'Database connection failed',
      error: error.message
    });
  }
};

/**
 * Get user by Discord ID
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getUserByDiscordId = async (req, res) => {
  try {
    const { discordId } = req.params;
    
    if (!discordId) {
      return res.status(400).json({
        success: false,
        message: 'Discord ID is required'
      });
    }
    
    const user = await User.findOne({ discordId });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    // Return user data formatted for Discord bot
    return res.status(200).json({
      success: true,
      data: {
        discordId: user.discordId,
        discordUsername: user.discordUsername,
        isMentor: user.isMentor,
        proficiencies: user.proficiencies,
        mentorApplication: user.mentorApplication,
        role: user.role,
        createdAt: user.createdAt,
        updatedAt: user.updatedAt
      }
    });
  } catch (error) {
    logger.error(`Get user by Discord ID error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get user',
      error: error.message
    });
  }
};

/**
 * Get all mentors formatted for Discord bot
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const getMentorsForBot = async (req, res) => {
  try {
    const mentors = await User.find({ 
      isMentor: true 
    }).select('discordId discordUsername proficiencies createdAt updatedAt');
    
    // Format mentors for Discord bot consumption
    const formattedMentors = mentors.map(mentor => ({
      discordId: mentor.discordId,
      discordUsername: mentor.discordUsername,
      proficiencies: mentor.proficiencies,
      selectedProficiencies: mentor.proficiencies
        .filter(p => p.isSelected)
        .map(p => p.category),
      createdAt: mentor.createdAt,
      updatedAt: mentor.updatedAt
    }));
    
    return res.status(200).json({
      success: true,
      data: formattedMentors,
      count: formattedMentors.length
    });
  } catch (error) {
    logger.error(`Get mentors for bot error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to get mentors',
      error: error.message
    });
  }
};

/**
 * Sync user data from Discord (create or update user)
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const syncUserFromDiscord = async (req, res) => {
  try {
    const { discordId, discordUsername, discordAvatar } = req.body;
    
    if (!discordId || !discordUsername) {
      return res.status(400).json({
        success: false,
        message: 'Discord ID and username are required'
      });
    }
    
    // Find existing user or create new one
    let user = await User.findOne({ discordId });
    
    if (user) {
      // Update existing user
      user.discordUsername = discordUsername;
      if (discordAvatar) {
        user.discordAvatar = discordAvatar;
      }
      user.updatedAt = new Date();
      await user.save();
      
      logger.info(`Updated user from Discord: ${discordUsername} (${discordId})`);
    } else {
      // Create new user
      user = new User({
        discordId,
        discordUsername,
        discordAvatar: discordAvatar || null,
        email: null, // Will be set when user logs in via OAuth
        isMentor: false,
        proficiencies: [],
        mentorApplication: { status: 'none' },
        role: 'user'
      });
      await user.save();
      
      logger.info(`Created new user from Discord: ${discordUsername} (${discordId})`);
    }
    
    return res.status(200).json({
      success: true,
      data: {
        discordId: user.discordId,
        discordUsername: user.discordUsername,
        isMentor: user.isMentor,
        proficiencies: user.proficiencies,
        mentorApplication: user.mentorApplication,
        role: user.role,
        isNew: !user.email // User is new if they don't have email (haven't done OAuth)
      },
      message: user.email ? 'User updated successfully' : 'User created successfully'
    });
  } catch (error) {
    logger.error(`Sync user from Discord error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to sync user',
      error: error.message
    });
  }
};

/**
 * Update user proficiencies
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 */
const updateUserProficiencies = async (req, res) => {
  try {
    const { discordId } = req.params;
    const { action, category } = req.body;
    
    if (!discordId) {
      return res.status(400).json({
        success: false,
        message: 'Discord ID is required'
      });
    }
    
    if (!action || !category) {
      return res.status(400).json({
        success: false,
        message: 'Action and category are required'
      });
    }
    
    if (!['add', 'remove'].includes(action)) {
      return res.status(400).json({
        success: false,
        message: 'Action must be "add" or "remove"'
      });
    }
    
    const user = await User.findOne({ discordId });
    
    if (!user) {
      return res.status(404).json({
        success: false,
        message: 'User not found'
      });
    }
    
    if (!user.isMentor) {
      return res.status(403).json({
        success: false,
        message: 'User is not a mentor'
      });
    }
    
    // Find existing proficiency
    const existingProfIndex = user.proficiencies.findIndex(p => p.category === category);
    
    if (action === 'add') {
      if (existingProfIndex >= 0) {
        // Update existing proficiency
        user.proficiencies[existingProfIndex].isSelected = true;
      } else {
        // Add new proficiency
        user.proficiencies.push({
          category,
          isSelected: true
        });
      }
    } else if (action === 'remove') {
      if (existingProfIndex >= 0) {
        // Remove proficiency or set to unselected
        user.proficiencies[existingProfIndex].isSelected = false;
      }
    }
    
    user.updatedAt = new Date();
    await user.save();
    
    logger.info(`Updated proficiencies for user ${discordUsername}: ${action} ${category}`);
    
    return res.status(200).json({
      success: true,
      data: {
        discordId: user.discordId,
        discordUsername: user.discordUsername,
        proficiencies: user.proficiencies,
        selectedProficiencies: user.proficiencies
          .filter(p => p.isSelected)
          .map(p => p.category)
      },
      message: `Proficiency ${action}ed successfully`
    });
  } catch (error) {
    logger.error(`Update user proficiencies error: ${error.message}`);
    return res.status(500).json({
      success: false,
      message: 'Failed to update proficiencies',
      error: error.message
    });
  }
};

module.exports = {
  getBotHealth,
  getUserByDiscordId,
  getMentorsForBot,
  syncUserFromDiscord,
  updateUserProficiencies
};

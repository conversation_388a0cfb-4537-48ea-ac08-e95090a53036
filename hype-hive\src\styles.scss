/* Global styles for HypeHive - Blue/Purple/Orange Theme */

:root {
  /* Primary Blue Colors */
  --primary-blue: #2563eb;
  --primary-blue-light: #3b82f6;
  --primary-blue-dark: #1d4ed8;
  --primary-blue-50: #eff6ff;
  --primary-blue-100: #dbeafe;
  --primary-blue-200: #bfdbfe;
  --primary-blue-500: #3b82f6;
  --primary-blue-600: #2563eb;
  --primary-blue-700: #1d4ed8;
  --primary-blue-900: #1e3a8a;

  /* Purple Colors */
  --purple: #8b5cf6;
  --purple-light: #a78bfa;
  --purple-dark: #7c3aed;
  --purple-50: #faf5ff;
  --purple-100: #f3e8ff;
  --purple-200: #e9d5ff;
  --purple-500: #8b5cf6;
  --purple-600: #7c3aed;
  --purple-700: #6d28d9;
  --purple-900: #581c87;

  /* Orange Colors */
  --orange: #f97316;
  --orange-light: #fb923c;
  --orange-dark: #ea580c;
  --orange-50: #fff7ed;
  --orange-100: #ffedd5;
  --orange-200: #fed7aa;
  --orange-500: #f97316;
  --orange-600: #ea580c;
  --orange-700: #c2410c;
  --orange-900: #9a3412;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Gradients */
  --gradient-primary: linear-gradient(135deg, var(--primary-blue) 0%, var(--purple) 50%, var(--orange) 100%);
  --gradient-blue-purple: linear-gradient(135deg, var(--primary-blue) 0%, var(--purple) 100%);
  --gradient-purple-orange: linear-gradient(135deg, var(--purple) 0%, var(--orange) 100%);
  --gradient-vertical: linear-gradient(180deg, var(--primary-blue) 0%, var(--purple) 50%, var(--orange) 100%);
  --gradient-horizontal: linear-gradient(90deg, var(--primary-blue) 0%, var(--purple) 50%, var(--orange) 100%);

  /* Semantic Colors */
  --success: #10b981;
  --warning: var(--orange);
  --error: #ef4444;
  --info: var(--primary-blue);

  /* Text Colors */
  --text-primary: var(--gray-900);
  --text-secondary: var(--gray-700);
  --text-muted: var(--gray-500);
  --text-inverse: #ffffff;

  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: var(--gray-50);
  --bg-tertiary: var(--gray-100);
  --bg-dark: var(--gray-900);

  /* Border Colors */
  --border-light: var(--gray-200);
  --border-medium: var(--gray-300);
  --border-dark: var(--gray-400);

  /* Shadow Colors */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Global Reset and Base Styles */
* {
  box-sizing: border-box;
}

body {
  margin: 0;
  padding: 0;
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  color: var(--text-primary);
  background-color: var(--bg-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  margin: 0;
  font-weight: 600;
  line-height: 1.2;
}

h1 { font-size: 2.5rem; }
h2 { font-size: 2rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

p {
  margin: 0 0 1rem 0;
}

/* Links */
a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color 0.2s ease;
}

a:hover {
  color: var(--primary-blue-dark);
}

/* Utility Classes */
.text-primary { color: var(--primary-blue); }
.text-purple { color: var(--purple); }
.text-orange { color: var(--orange); }
.text-muted { color: var(--text-muted); }
.text-center { text-align: center; }

.bg-gradient { background: var(--gradient-primary); }
.bg-blue-purple { background: var(--gradient-blue-purple); }
.bg-purple-orange { background: var(--gradient-purple-orange); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }

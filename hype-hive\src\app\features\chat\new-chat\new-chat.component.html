<div class="new-chat-container">
  <div class="new-chat-header">
    <button mat-icon-button (click)="cancel()" aria-label="Cancel">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <h1>New Message</h1>
  </div>

  <div class="new-chat-content">
    <form [formGroup]="chatForm" (ngSubmit)="createChat()">
      <div class="form-group">
        <mat-form-field appearance="outline" class="search-input">
          <mat-label>Search users</mat-label>
          <input 
            matInput 
            formControlName="searchTerm" 
            placeholder="Enter username" 
            (input)="search(chatForm.get('searchTerm')?.value)">
          <mat-icon matSuffix *ngIf="!isSearching">search</mat-icon>
          <mat-spinner matSuffix diameter="20" *ngIf="isSearching"></mat-spinner>
        </mat-form-field>

        <div class="search-results" *ngIf="searchResults.length > 0">
          <mat-nav-list>
            <a mat-list-item *ngFor="let user of searchResults" (click)="addUser(user)">
              <img matListItemAvatar [src]="user.discordAvatar || 'assets/images/default-avatar.png'" alt="Avatar">
              <span matListItemTitle>{{ user.discordUsername }}</span>
              <span matListItemLine *ngIf="user.isMentor" class="mentor-badge">Mentor</span>
            </a>
          </mat-nav-list>
        </div>
      </div>

      <div class="selected-users" *ngIf="selectedUsers.length > 0">
        <h3>Selected Users</h3>
        <div class="user-chips">
          <mat-chip-set>
            <mat-chip 
              *ngFor="let user of selectedUsers" 
              (removed)="removeUser(user._id || '')">
              {{ user.discordUsername }}
              <mat-icon matChipRemove>cancel</mat-icon>
            </mat-chip>
          </mat-chip-set>
        </div>
      </div>

      <div class="form-group group-chat-toggle">
        <mat-slide-toggle formControlName="isGroupChat">
          Create Group Chat
        </mat-slide-toggle>
      </div>

      <div class="form-group" *ngIf="chatForm.get('isGroupChat')?.value">
        <mat-form-field appearance="outline">
          <mat-label>Group Name</mat-label>
          <input matInput formControlName="groupName" placeholder="Enter group name">
          <mat-error *ngIf="chatForm.get('groupName')?.hasError('required')">
            Group name is required
          </mat-error>
          <mat-error *ngIf="chatForm.get('groupName')?.hasError('minlength')">
            Group name must be at least 3 characters
          </mat-error>
          <mat-error *ngIf="chatForm.get('groupName')?.hasError('maxlength')">
            Group name cannot exceed 50 characters
          </mat-error>
        </mat-form-field>
      </div>

      <div class="error-message" *ngIf="errorMessage">
        <mat-icon>error</mat-icon>
        <span>{{ errorMessage }}</span>
      </div>

      <div class="form-actions">
        <button 
          mat-button 
          type="button" 
          (click)="cancel()">
          Cancel
        </button>
        <button 
          mat-raised-button 
          color="primary" 
          type="submit" 
          [disabled]="isLoading || selectedUsers.length === 0 || (chatForm.get('isGroupChat')?.value && !chatForm.get('groupName')?.valid)">
          <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
          <span *ngIf="!isLoading">Create Chat</span>
        </button>
      </div>
    </form>
  </div>
</div>
